import {applyPatch, PatchApplyResult} from './apply';
import {parsePatchString} from './parse';

export interface PatchResult {
    type: 'success' | 'fuzzyMatch' | 'parseError' | 'partialSuccess';
    newContent?: string;
    deletedCount?: number;
    insertedCount?: number;
    fuzzyMatches?: Array<{
        searchContent: string[];
        matchedContent: string[];
        similarity: number;
    }>;
    error?: string;
    successCount?: number;
    totalCount?: number;
}

export function patchContent(content: string, patch: string): PatchResult {
    try {
        const patches = parsePatchString(patch);
        const {deletedCount, insertedCount} = patches.reduce(
            (result, patch) => {
                const deleted = patch.search.filter(v => !patch.replace.includes(v));
                const inserted = patch.replace.filter(v => !patch.search.includes(v));

                return {
                    deletedCount: result.deletedCount + deleted.length,
                    insertedCount: result.insertedCount + inserted.length,
                };
            },
            {deletedCount: 0, insertedCount: 0}
        );

        try {
            const patchResult = applyPatch(content, patches);
            if (patchResult.type === 'success') {
                return {
                    type: 'success',
                    newContent: patchResult.content,
                    deletedCount,
                    insertedCount,
                };
            } else {
                // Partial success case
                return {
                    type: 'partialSuccess',
                    newContent: patchResult.content,
                    deletedCount,
                    insertedCount,
                    fuzzyMatches: patchResult.fuzzyMatches,
                    successCount: patchResult.successCount,
                    totalCount: patchResult.totalCount
                };
            }
        } catch (error) {
            // 如果错误中包含模糊匹配信息，返回模糊匹配结果
            if (error instanceof Error && (error as any).fuzzyMatches) {
                return {
                    type: 'fuzzyMatch',
                    fuzzyMatches: (error as any).fuzzyMatches
                };
            }
            // 其他错误继续抛出
            throw error;
        }
    } catch (error) {
        // 解析错误
        if (error instanceof Error) {
            return {
                type: 'parseError',
                error: error.message
            };
        }
        throw error;
    }
}
