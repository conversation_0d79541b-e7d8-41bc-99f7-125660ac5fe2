import {builtinTools, ModelToolCallInput, StreamingToolParser} from '../../tool';
import {FixToolCallView, renderFixToolCallPrompt} from '../../prompt';
import {newUuid} from '../../utils/id';
import {AssistantTextMessage} from '../../message';
import {createModelClient, ModelChatOptions} from '../../model';
import {ToolInputError} from './utils';

async function* iterable(content: string): AsyncIterable<string> {
    yield content;
}

export async function parseToolMessage(content: string) {
    const parser = new StreamingToolParser();
    const message = new AssistantTextMessage(newUuid());
    for await (const chunk of parser.parse(iterable(content))) {
        message.addChunk(chunk);
    }
    const toolCall = message.toToolCallMessage();

    if (!toolCall) {
        throw new Error('No tool call found in response');
    }

    return toolCall.getToolCallInputWithSource() ?? null;
}

function formatErrorMessage(error: ToolInputError) {
    switch (error.type) {
        case 'parameterMissing':
            return `Missing value for required parameter "${error.parameter}", this may be caused by empty content in <${error.parameter}> tag or missing <${error.parameter}> tag.`;
        case 'parameterType':
            return `Parameter "${error.parameter}" is not of type ${error.expectedType}.`;
        default:
            return `Parameters have unknown error: ${error.message}.`;
    }
}

async function fix(view: FixToolCallView) {
    const model = createModelClient();
    const prompt = renderFixToolCallPrompt(view);
    const chatOptions: ModelChatOptions = {
        messages: [
            {role: 'user', content: prompt},
        ],
    };
    const [result] = await model.chat(chatOptions);
    const toolCall = await parseToolMessage(result.content);
    return toolCall;
}

export interface ToolCallFixOptions {
    input: ModelToolCallInput;
    response: string;
    error: ToolInputError;
}

export async function fixToolCall(options: ToolCallFixOptions) {
    const {input, response, error} = options;
    const tool = builtinTools.find(v => v.name === input.name);

    if (!tool) {
        throw new Error('Cannot fix tool call without a determined tool name');
    }

    const view: FixToolCallView = {
        tool,
        errorContent: response,
        errorMessage: formatErrorMessage(error),
    };
    const {default: pRetry} = await import('p-retry');
    const result = await pRetry(
        () => fix(view),
        {retries: 3}
    );

    return result;
}
