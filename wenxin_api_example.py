from qianfan import Qi<PERSON><PERSON>

# 方式一：使用API Key值鉴权
# 替换下列示例中参数，将your_APIKey替换为真实值
api_key = "your_APIKey"
app_id = ""  # 选填，不填写则使用默认appid

# 方式二：使用安全认证AK/SK鉴权
# 替换下列示例中参数，安全认证Access Key替换your_iam_ak，Secret Key替换your_iam_sk
# access_key = "your_iam_ak"
# secret_key = "your_iam_sk"
# app_id = ""  # 选填，不填写则使用默认appid

client = Qianfan(
    # 使用方式一
    api_key=api_key,
    app_id=app_id,
    
    # 或使用方式二
    # access_key=access_key,
    # secret_key=secret_key
)

# 创建对话请求
completion = client.chat.completions.create(
    model="ernie-3.5-8k",  # 指定模型
    messages=[
        {"role": "system", "content": "你是一个有帮助的助手"},
        {"role": "user", "content": "你好，请介绍一下你自己"}
    ],
    temperature=0.7,  # 控制生成随机性
    top_p=0.8,  # 控制生成多样性
    stream=False  # 是否使用流式响应
)

# 打印响应结果
print(completion.choices[0].message.content)
