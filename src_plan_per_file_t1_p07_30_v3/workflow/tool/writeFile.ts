import fs from 'node:fs/promises';
import {writeFileParameters, WriteFileParameter} from '../../tool';
import {ToolImplementBase, ToolRunResult} from './utils';
import {stringifyError} from '../../utils/error';

export class WriteFileToolImplement extends ToolImplementBase<WriteFileParameter> {
    constructor() {
        super(writeFileParameters);
    }

    protected parseArgs(args: Record<string, string | undefined>) {
        return {
            path: args.path,
            content: args.content,
        };
    }

    protected async execute(args: WriteFileParameter): Promise<ToolRunResult> {
        try {
            await fs.writeFile(args.path, args.content);
            return {
                type: 'success',
                finished: false,
                output:
                    `The content is written to ${args.path} as you wish, you might need to run it to confirm your change.`,
            };
        }
        catch (ex) {
            return {
                type: 'executeError',
                output: `Write file ${args.path} failed: ${stringifyError(ex)}`,
            };
        }
    }
}
