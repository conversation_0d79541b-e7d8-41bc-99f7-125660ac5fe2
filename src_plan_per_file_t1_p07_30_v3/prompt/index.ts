import {ToolDescription} from '../tool';
import {renderRuleSection, renderScratchStartRuleSection} from './rule';
import {renderFormatSection} from './format';
import {renderToolSection} from './tool';
import {renderRootEntriesSection} from './rootEntries';
import {renderObjectiveSection} from './objective';
import {EmbeddingContextMode, EmbeddingSearchResultItem, renderEmbeddingSection} from './embedding';

export {renderFixToolCallPrompt} from './fixToolCall';
export type {FixToolCallView} from './fixToolCall';
export type {CodePosition, EmbeddingSearchResultItem, EmbeddingContextMode} from './embedding';

export interface InboxPromptView {
    tools: ToolDescription[];
    embedding: EmbeddingSearchResultItem[];
    embeddingContextMode: EmbeddingContextMode;
    rootEntries: string[];
}

export async function renderInboxSystemPrompt(view: InboxPromptView) {
    const parts = [
        view.rootEntries.length ? renderRuleSection() : renderScratchStartRuleSection(),
        renderFormatSection(),
        renderToolSection(view),
        '# Context',
        'This section provides some already known information for user\'s request.',
        view.rootEntries.length ? renderRootEntriesSection(view.rootEntries) : '',
        view.embedding.length ? renderEmbeddingSection(view.embedding, view.embeddingContextMode) : '',
        renderObjectiveSection(),
    ];
    return parts.filter(v => !!v).join('\n\n');
}
