import {ModelConfiguration, ModelClient, ModelType} from './interface';
import {OpenAiModelClient} from './openai';
import {ErnieModelClient} from './ernie';
import {ErnieCodeModelClient} from './ernieCode';
import {LocalDSModelClient} from './local_ds';
import {HuoshanDSModelClient} from './huoshan_ds';

export type {
    ChatUserMessagePayload,
    ChatAssistantMessagePayload,
    ModelChatOptions,
    ModelClient,
    ModelConfiguration,
    ModelMetaResponse,
    ModelUsage,
    ModelResponse,
    ModelStreamingResponse,
    ChatInputPayload,
} from './interface';

function validateModelConfiguration(config: ModelConfiguration): void {
    if (!config.modelName) {
        throw new Error('Require modelName to create a model client');
    }

    if (!config.apiKey) {
        throw new Error('Require apiKey to create a model client');
    }
}

function readModelConfig(): ModelConfiguration {
    const modelType = (process.env.MODEL_TYPE ?? 'claude') as ModelType;

    switch (modelType) {
        case 'claude':
            return {
                type: 'claude',
                modelName: process.env.MODEL_NAME ?? 'anthropic/claude-3.7-sonnet',
                apiKey: process.env.CLAUDE_API_KEY ?? process.env.API_KEY ?? '',
            };
        case 'ernie':
            return {
                type: 'ernie',
                modelName: process.env.MODEL_NAME ?? 'ernie-3.5-128k',
                apiKey: process.env.ERNIE_API_KEY ?? '', // Expected format: "clientId:clientSecret"
            };
        case 'local_ds':
            return {
                type: 'local_ds',
                modelName: process.env.MODEL_NAME ?? 'dsr1',
                apiKey: process.env.LOCAL_DS_API_KEY ?? '',
            };
        case 'huoshan_ds':
            return {
                type: 'huoshan_ds',
                modelName: process.env.MODEL_NAME ?? 'deepseek-v3-241226',
                apiKey: process.env.HUOSHAN_DS_API_KEY ?? process.env.LOCAL_DS_API_KEY ?? '',
            };
        case 'ernie-code':
            return {
                type: 'ernie-code',
                modelName: process.env.MODEL_NAME ?? 'ernie-3.5-128k',
                apiKey: process.env.HUOSHAN_DS_API_KEY ?? process.env.LOCAL_DS_API_KEY ?? 'key',
            };
        default:
            throw new Error(`Unsupported model type: ${modelType}`);
    }
}

export function createModelClient(): ModelClient {
    const config = readModelConfig();
    //validateModelConfiguration(config);

    switch (config.type) {
        case 'claude':
            return new OpenAiModelClient(config);
        case 'ernie':
            return new ErnieModelClient(config);
        case 'ernie-code':
            return new ErnieCodeModelClient(config);
        case 'local_ds':
            return new LocalDSModelClient({
                apiKey: config.apiKey,
                modelName: config.modelName,
            });
        case 'huoshan_ds':
            return new HuoshanDSModelClient({
                apiKey: config.apiKey,
                modelName: config.modelName,
            });
        default:
            throw new Error(`Unsupported model type: ${config.type}`);
    }
}
