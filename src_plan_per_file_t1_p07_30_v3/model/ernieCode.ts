import {
    ModelClient,
    ModelConfiguration,
    ModelMetaResponse,
    ModelResponse,
    ModelChatOptions,
    ModelStreamingResponse,
} from './interface';

interface ErnieResponse {
    result: {
        response: {
            utterance: string;
        };
    };
}

interface ErnieMessage {
    role: 'user' | 'assistant';
    utterance: string;
}

export class ErnieCodeModelClient implements ModelClient {
    private readonly config: ModelConfiguration;

    constructor(config: ModelConfiguration) {
        if (config.type !== 'ernie-code') {
            throw new Error(`ErnieModelClient requires config.type to be 'ernie-code', but got '${config.type}'`);
        }
        this.config = config;
    }

    async chat(options: ModelChatOptions): Promise<[ModelResponse, ModelMetaResponse]> {
        const messages: ErnieMessage[] = [];
        for (const input of options.messages) {
            messages.push({role: input.role, utterance: input.content});
        }

        const body = {
            context: messages,
            system: options.systemPrompt,
            reasoning_mode: false,
        };
        const response = await fetch(
            'http://10.95.117.210:8090/api/chat',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            }
        );

        if (!response.ok) {
            throw new Error(`Ernie API request failed: ${response.statusText}`);
        }

        const data = await response.json() as ErnieResponse;
        return [
            {
                type: 'text',
                content: data.result.response.utterance,
            },
            {
                type: 'meta',
                model: 'ernie-4.5-turbo-code',
                usage: {
                    inputTokens: null,
                    outputTokens: null,
                },
            },
        ];
    }

    async *chatStreaming(options: ModelChatOptions): AsyncIterable<ModelStreamingResponse> {
        const [data, meta] = await this.chat(options);
        yield data;
        yield meta;
    }
}
