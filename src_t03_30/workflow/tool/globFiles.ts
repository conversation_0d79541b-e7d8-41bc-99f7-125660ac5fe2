import path from 'node:path';
import fs from 'node:fs/promises';
import {findFilesByGlobParameters, FindFilesByGlobParameter} from '../../tool';
import {stringifyError} from '../../utils/error';
import {resultMarkdown, ToolImplementBase, ToolRunResult} from './utils';

export class GlobFilesToolImplement extends ToolImplementBase<FindFilesByGlobParameter> {
    constructor() {
        super(findFilesByGlobParameters);
    }

    protected parseArgs(args: Record<string, string>): FindFilesByGlobParameter {
        return {
            glob: args.glob,
        };
    }

    protected async execute(args: FindFilesByGlobParameter): Promise<ToolRunResult> {
        const {globby} = await import('globby');
        const cwd = process.cwd();
        const MAX_LINES = 100; // Maximum number of lines to return

        // Check if .gitignore file exists
        let hasGitignore = false;
        try {
            await fs.access(path.join(cwd, '.gitignore'));
            hasGitignore = true;
        } catch (error) {
            if ((error as NodeJS.ErrnoException).code === 'ENOENT') {
                console.warn('No .gitignore file found in the current directory');
            } else {
                console.warn('Error checking .gitignore file:', error);
            }
        }

        try {
            const files = await globby(args.glob, {
                gitignore: true,  // 这会自动应用.gitignore规则
                expandDirectories: false, // 不展开目录
                dot: false,  // 设置为false以排除隐藏文件（以点开头的文件）
                onlyFiles: true,
                ignore:  ['.*', '**/.*', '!.'], // 排除所有隐藏目录及其内容
            });

            if (files.length) {
                const displayedFiles = files.slice(0, MAX_LINES);
                const remainingFiles = files.length - displayedFiles.length;
                const output = displayedFiles.map(v => path.relative(cwd, v)).join('\n');
                const truncationMessage = remainingFiles > 0 ? `\n\n...${remainingFiles} more files not shown. You may need to adjust your request to see more.` : '';

                return {
                    type: 'success',
                    finished: false,
                    output: resultMarkdown(
                        `Files matching glob ${args.glob}:`,
                        output +
                        truncationMessage +
                        (hasGitignore ? '\n\nNote: Files matching patterns in .gitignore were excluded' : '') +
                        '\n\nNote: Hidden files and directories (starting with .) were excluded'
                    ),
                };
            }

            return {
                type: 'success',
                finished: false,
                output: `There are no files matching glob \`${args.glob}\`` +
                    (hasGitignore ? '\n(Note: Files matching patterns in .gitignore were excluded)' : '') +
                    '\n(Note: Hidden files and directories (starting with .) were excluded)'
            };
        }
        catch (ex) {
            return {
                type: 'executeError',
                output: `Unable to find files with pattern ${args.glob}: ${stringifyError(ex)}`,
            };
        }
    }
}
