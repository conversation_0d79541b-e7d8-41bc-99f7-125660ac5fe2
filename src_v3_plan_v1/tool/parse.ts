import {StreamXmlParser, XmlParseTagEndChunk, XmlParseTagStartChunk, XmlParseTextChunk} from '../utils/string';
import {isToolName, ToolName} from '.';

interface TextChunk {
    type: 'text';
    content: string;
}

interface ThinkingStartChunk {
    type: 'thinkingStart';
    source: string;
}

interface ThinkingDeltaChunk {
    type: 'thinkingDelta';
    source: string;
}

interface ThinkingEndChunk {
    type: 'thinkingEnd';
    source: string;
}

interface TextInToolChunk {
    type: 'textInTool';
    source: string;
}

interface ToolStartChunk {
    type: 'toolStart';
    toolName: ToolName;
    source: string;
}

interface ToolDeltaChunk {
    type: 'toolDelta';
    arguments: Record<string, string>;
    source: string;
}

interface ToolEndChunk {
    type: 'toolEnd';
    source: string;
}

export type ToolParsedChunk =
    | TextChunk
    | ThinkingStartChunk
    | ThinkingDeltaChunk
    | ThinkingEndChunk
    | TextInToolChunk
    | ToolStartChunk
    | ToolDeltaChunk
    | ToolEndChunk;

export class StreamingToolParser {
    private tagStack: string[] = [];
    private completedToolCall: boolean = false;

    async *parse(stream: AsyncIterable<string>): AsyncIterable<ToolParsedChunk> {
        const parser = new StreamXmlParser();
        for await (const chunk of parser.parse(stream)) {
            if (this.completedToolCall) {
                break;
            }
            switch (chunk.type) {
                case 'text':
                    yield* this.yieldForTextChunk(chunk);
                    break;
                case 'tagStart':
                    yield* this.yieldForTagStart(chunk);
                    break;
                case 'tagEnd':
                    yield* this.yieldForTagEnd(chunk);
                    break;
            }
        }
    }

    *yieldForTextChunk(chunk: XmlParseTextChunk): Iterable<ToolParsedChunk> {
        console.log('[yieldForTextChunk]', {tagStack: this.tagStack, chunk});
        const activeTag = this.tagStack.at(-1);
        // `<thinking>` tag has plain text inside it
        if (activeTag === 'think') {
            console.log('85')
            // 对于thinking标签内的文本，只输出一次，作为thinkingDelta
            yield {type: 'thinkingDelta', source: chunk.content};
        }
        // We don't allow text content in top level tag, all tool calls contains only parameters
        else if (activeTag) {
            if (this.tagStack.length > 1) {
                console.log('91')
                yield {type: 'toolDelta', arguments: {[activeTag]: chunk.content}, source: chunk.content};
            }
            else {
                console.log('94')
                yield {type: 'textInTool', source: chunk.content};
            }
        }
        else {
            console.log('98')
            // 对于标签外的文本，只输出一次，作为普通文本
            yield {type: 'text', content: chunk.content};
        }
    }

    *yieldForTagStart(chunk: XmlParseTagStartChunk): Iterable<ToolParsedChunk> {
        console.log('[yieldForTagStart]', {tagStack: this.tagStack, chunk});
        if (!chunk?.tagName || !chunk?.source) {
            yield {type: 'text', content: chunk?.source || ''};
            return;
        }

        const activeTag = this.tagStack.at(-1);
        if (activeTag === 'think') {
            yield {type: 'thinkingDelta', source: chunk.source};
            return;
        }

        if (activeTag) {
            if (isToolName(activeTag)) {
                // 验证新标签名有效性
                if (!chunk.tagName || !/^[a-zA-Z0-9_]+$/.test(chunk.tagName)) {
                    yield {type: 'textInTool', source: chunk.source};
                    return;
                }
                this.tagStack.push(chunk.tagName);
                yield {type: 'toolDelta', arguments: {[chunk.tagName]: ''}, source: chunk.source};
            } else {
                yield {type: 'toolDelta', arguments: {[activeTag]: chunk.source || ''}, source: chunk.source};
            }
            return;
        }

        if (chunk.tagName === 'think') {
            this.tagStack.push(chunk.tagName);
            yield {type: 'thinkingStart', source: chunk.source};
            return;
        }

        if (isToolName(chunk.tagName)) {
            this.tagStack.push(chunk.tagName);
            yield {type: 'toolStart', toolName: chunk.tagName, source: chunk.source};
            return;
        }

        yield {type: 'text', content: chunk.source};
    }

    *yieldForTagEnd(chunk: XmlParseTagEndChunk): Iterable<ToolParsedChunk> {
        console.log('[yieldForTagEnd]', {tagStack: this.tagStack, chunk});
        console.log(chunk.tagName)


        if (!chunk?.tagName || !chunk?.source) {
            console.log('149');
            yield {type: 'text', content: chunk?.source || ''};
            return;
        }

        if (!this.tagStack.length) {
            console.log('155');
            yield {type: 'text', content: chunk.source};
            return;
        }

        const currentTag = this.tagStack.at(-1);
        if (!currentTag) {
            console.log('162');
            yield {type: 'text', content: chunk.source};
            return;
        }

        if (currentTag === 'think') {
            console.log('168');
            if (chunk.tagName === 'think') {
                yield {type: 'thinkingEnd', source: chunk.source};
                this.tagStack.pop();
            } else {
                yield {type: 'thinkingDelta', source: chunk.source};
            }
            return;
        }

        if (isToolName(currentTag)) {
            console.log('179');
            if (chunk.tagName === currentTag) {
                this.tagStack.pop();
                yield {type: 'toolEnd', source: chunk.source};
                this.completedToolCall = true;
            } else {
                yield {type: 'textInTool', content: chunk.source};
            }
            return;
        }

        if (chunk.tagName === currentTag) {
            console.log('191');
            this.tagStack.pop();
            yield {type: 'toolDelta', arguments: {[currentTag]: ''}, source: chunk.source};
        } else {
            yield {type: 'toolDelta', arguments: {[currentTag]: chunk.source || ''}, source: chunk.source};
        }
    }
}
