import { ChatInputPayload, ModelClient, ModelConfiguration, ModelMetaResponse, ModelResponse, ModelChatOptions } from './interface';
import { ModelResponseMetaRecord } from './utils';

interface ErnieResponse {
    id: string;
    object: string;
    created: number;
    result: string;
    is_truncated: boolean;
    need_clear_history: boolean;
    finish_reason: string;
    usage: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}

export class ErnieModelClient implements ModelClient {
    private readonly config: ModelConfiguration;
    private accessToken: string | null = null;
    private accessTokenExpiry: number = 0;

    constructor(config: ModelConfiguration) {
        if (config.type !== 'ernie') {
            throw new Error(`ErnieModelClient requires config.type to be 'ernie', but got '${config.type}'`);
        }
        this.config = config;
    }

    private async getAccessToken(): Promise<string> {

        const apiKey = this.config.apiKey;
        this.accessToken = apiKey;

        return this.accessToken;
    }

    async chat(options: ModelChatOptions): Promise<[ModelResponse, ModelMetaResponse]> {
        const accessToken = await this.getAccessToken();

        const response = await fetch(
            `https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/${this.config.modelName}?access_token=${accessToken}`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    messages: options.messages,
                    system: options.systemPrompt,
                    stream: false,
                    top_p: 0.9,
                    temperature: 0.7,
                    max_output_tokens: this.config.modelName === 'ernie-4.0-turbo-128k' ? 4000 : 8000,
                }),
            }
        );

        if (!response.ok) {
            throw new Error(`Ernie API request failed: ${response.statusText}`);
        }

        const data: ErnieResponse = await response.json();
        const record = new ModelResponseMetaRecord(this.config.modelName);

        record.setInputTokens(data.usage.prompt_tokens);
        record.addOutputTokens(data.usage.completion_tokens);

        return [
            {type: 'text', content: data.result},
            record.toResponseMeta(),
        ];
    }

    async *chatStreaming(options: ModelChatOptions): AsyncIterable<ModelStreamingResponse> {
        // Since Ernie API doesn't support streaming directly, we'll implement it by
        // first getting the complete response and then simulating streaming
        const accessToken = await this.getAccessToken();

        const response = await fetch(
            `https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/${this.config.modelName}?access_token=${accessToken}`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    messages: options.messages,
                    system: options.systemPrompt,
                    top_p: 0.9,
                    temperature: 0.7,
                    max_output_tokens: this.config.modelName === 'ernie-4.0-turbo-128k' ? 4000 : 8000,
                }),
            }
        );

        if (!response.ok) {
            throw new Error(`Ernie API request failed: ${response.statusText}`);
        }

        const data: ErnieResponse = await response.json();
        const metaRecord = new ModelResponseMetaRecord(this.config.modelName);

        // Simulate streaming by yielding the text in chunks
        const chunkSize = 4; // Yield 4 characters at a time
        const text = data.result;
        for (let i = 0; i < text.length; i += chunkSize) {
            const chunk = text.slice(i, i + chunkSize);
            yield {type: 'text', content: chunk} as const;
        }

        metaRecord.setInputTokens(data.usage.prompt_tokens);
        metaRecord.addOutputTokens(data.usage.completion_tokens);
        yield metaRecord.toResponseMeta();
    }
}
