import fetch from 'node-fetch';
import fs from 'fs';
import path from 'path';
import { ModelClient, ModelChatOptions, ModelResponse, ModelStreamingResponse, ModelMetaResponse } from './interface';
import { ModelResponseMetaRecord } from './utils';

//const API_URL = 'https://qianfan.baidubce.com/v2/chat/completions';
const API_URL = 'https://ark.cn-beijing.volces.com/api/v3/chat/completions';

export class HuoshanDSModelClient implements ModelClient {
    private readonly apiKey: string;
    private readonly modelName: string;

    constructor(config: { apiKey: string; modelName: string }) {
        this.apiKey = config.apiKey;
        this.modelName = config.modelName;
    }

    private saveModelInput(messages: any[], outputPath: string, error?: string) {
        try {
            const directory = path.dirname(outputPath);
            const inputPath = path.join(directory, 'case-1-model-input.json');

            const newRecord = {
                messages,
                timestamp: new Date().toISOString(),
                apiUrl: API_URL,
                modelName: this.modelName,
                error: error || null
            };

            let records = [];
            try {
                if (fs.existsSync(inputPath)) {
                    const content = fs.readFileSync(inputPath, 'utf8');
                    records = JSON.parse(content);
                    if (!Array.isArray(records)) {
                        records = [records];
                    }
                }
            } catch (e) {
                console.error('Error reading existing file:', e);
            }

            records.push(newRecord);
            fs.writeFileSync(inputPath, JSON.stringify(records, null, 2));
            console.log(`Model input saved to ${inputPath}`);
        } catch (error) {
            console.error('Failed to save model input:', error);
        }
    }

    async chat(options: ModelChatOptions): Promise<[ModelResponse, ModelMetaResponse]> {
        const messages = [...options.messages];
        if (options.systemPrompt) {
            messages.unshift(
                { role: 'user', content: options.systemPrompt },
                { role: 'assistant', content: '好的，我明白了，我是Zulu，我将全力落实之后用户的所有请求' }
            );
        }

        const outputPath = process.env.CURRENT_OUTPUT_PATH || '';
        if (outputPath) {
            this.saveModelInput(messages, outputPath);
        }

        let lastError;
        let retryDelay = 1000; // 初始重试延迟1秒

        while (true) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 600000); // 10分钟超时

                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiKey.trim()}`,
                    },
                    body: JSON.stringify({
                        model: this.modelName,
                        messages,
                        stream: false,
                        max_tokens: 8000,
                        temperature: 1.0
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    const error = `Huoshan DS API request failed: ${response.statusText}`;
                    if (outputPath) {
                        this.saveModelInput(messages, outputPath, error);
                    }
                    throw new Error(error);
                }

                const data = await response.json();

                const record = new ModelResponseMetaRecord(this.modelName);
                record.setInputTokens(data.usage?.prompt_tokens);
                record.addOutputTokens(data.usage?.completion_tokens);

                return [
                    { type: 'text', content: data.choices[0].message.content },
                    record.toResponseMeta(),
                ];
            } catch (error) {
                lastError = error;
                const errorType = error.code || error.name || 'UnknownError';
                console.error(`[HuoshanDS] Request failed (${errorType}), retrying in ${retryDelay}ms:`, error.message);

                if (error.code === 'ETIMEDOUT') {
                    console.error('[HuoshanDS] Network timeout detected. Possible causes:');
                    console.error('- Unstable network connection');
                    console.error('- Server overload');
                    console.error('- High latency connection');
                }

                await new Promise(resolve => setTimeout(resolve, retryDelay));
                retryDelay = Math.min(retryDelay * 2, 3000); // 指数退避，最大3秒
            }
        }
    }

    async *chatStreaming(options: ModelChatOptions): AsyncGenerator<ModelStreamingResponse> {
        const messages = [...options.messages];
        if (options.systemPrompt) {
            messages.unshift(
                { role: 'user', content: options.systemPrompt },
                { role: 'assistant', content: '好的，我明白了，我是Zulu，我将全力落实之后用户的所有请求' }
            );
        }

        const outputPath = process.env.CURRENT_OUTPUT_PATH || '';
        if (outputPath) {
            this.saveModelInput(messages, outputPath);
        }

        let lastError;
        let retryDelay = 1000; // 初始重试延迟1秒

        while (true) {
            try {
                console.log('Fetching complete response...');
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 600000); // 10分钟超时

                const response = await fetch(API_URL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.apiKey.trim()}`,
                    },
                    body: JSON.stringify({
                        model: this.modelName,
                        messages,
                        stream: false, // 使用非流式请求
                        max_tokens: 8000,
                        temperature: 1.
                    }),
                    signal: controller.signal
                });

                clearTimeout(timeoutId);

                if (!response.ok) {
                    const error = `Huoshan DS API request failed with status ${response.status}: ${response.statusText}`;
                    console.error('[HuoshanDS] API request failed:', error);
                    try {
                        const errorBody = await response.text();
                        console.error('[HuoshanDS] API error response:', errorBody);
                    } catch (e) {
                        console.error('[HuoshanDS] Failed to read error response:', e);
                    }

                    if (outputPath) {
                        this.saveModelInput(messages, outputPath, error);
                    }
                    throw new Error(error);
                }

                let data;
                try {
                    data = await response.json();
                    console.debug('[HuoshanDS] Received API response:', JSON.stringify(data, null, 2));
                } catch (e) {
                    const error = `Failed to parse API response: ${e instanceof Error ? e.message : String(e)}`;
                    console.error('[HuoshanDS]', error);
                    if (outputPath) {
                        this.saveModelInput(messages, outputPath, error);
                    }
                    throw new Error(error);
                }

                if (!data?.choices?.[0]?.message?.content) {
                    const error = `Invalid response format from API. Received: ${JSON.stringify(data)}`;
                    console.error('[HuoshanDS]', error);
                    console.log('Model input before error:', messages);
                    if (outputPath) {
                        this.saveModelInput(messages, outputPath, error);
                    }
                    throw new Error(error);
                }

                // 模拟流式传输响应
                const chunkSize = 4;
                const text = (data.choices[0].message.reasoning_content ? `<think>\n${data.choices[0].message.reasoning_content}\n</think>` : '') + data.choices[0].message.content;
                const metaRecord = new ModelResponseMetaRecord(this.modelName);

                try {
                    // 按块发送文本内容
                    for (let i = 0; i < text.length; i += chunkSize) {
                        const chunk = text.slice(i, i + chunkSize);
                        yield { type: 'text', content: chunk } as const;
                    }

                    // 在内容流式传输后设置并发送使用信息
                    if (data.usage) {
                        metaRecord.setInputTokens(data.usage.prompt_tokens);
                        metaRecord.addOutputTokens(data.usage.completion_tokens);
                    }
                    yield metaRecord.toResponseMeta();
                    return;
                } catch (streamError) {
                    throw new Error(`Streaming error: ${streamError.message}`);
                }
            } catch (error) {
                lastError = error;
                const errorType = error.code || error.name || 'UnknownError';
                console.error(`[HuoshanDS] Request failed (${errorType}), retrying in ${retryDelay}ms:`, error.message);

                if (error.code === 'ETIMEDOUT') {
                    console.error('[HuoshanDS] Network timeout detected. Possible causes:');
                    console.error('- Unstable network connection');
                    console.error('- Server overload');
                    console.error('- High latency connection');
                }

                await new Promise(resolve => setTimeout(resolve, retryDelay));
                retryDelay = Math.min(retryDelay * 2, 3000); // 指数退避，最大3秒
            }
        }
    }
}
