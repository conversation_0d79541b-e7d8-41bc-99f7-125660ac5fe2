import path from 'node:path';
import fs from 'node:fs/promises';
import 'dotenv/config';
import yargs from 'yargs';
import {hideBin} from 'yargs/helpers';
import {ComposerRequest, Worker, WorkerOptions} from './worker';
import {newUuid} from './utils/id';
import {GlobalConfig} from './worker/interface';

interface Input {
    /** 用户发起的消息内容 */
    query: string;
    /** Embedding接口的repoId参数 */
    embeddingRepoId?: string;
    /** 对应`output`参数，可以用当前项目为基础的相对路径 */
    output?: string;
    /** 通用环境文本，将被添加到每个查询中 */
    environment?: string;
}

function assert(fn: () => boolean, message: string) {
    if (!fn()) {
        throw new Error(message);
    }
}

async function readConfig(file: string | undefined): Promise<GlobalConfig> {
    if (!file) {
        return {
            embeddingConetxtMode: 'chunk',
            embeddingExcludesLanguage: [],
            embeddingMinDistance: 0,
        };
    }

    const input: Partial<GlobalConfig> = JSON.parse(await fs.readFile(file, 'utf-8'));
    const config: GlobalConfig = {
        embeddingConetxtMode: input.embeddingConetxtMode ?? 'chunk',
        embeddingExcludesLanguage: input.embeddingExcludesLanguage ?? [],
        embeddingMinDistance: input.embeddingMinDistance ?? 0,
    };
    assert(
        () => ['chunk', 'fullContent', 'nameOnly'].includes(config.embeddingConetxtMode),
        'embeddingConetxtMode should be one of `chunk`, `fullContent` or `nameOnly`'
    );
    assert(
        () => Array.isArray(config.embeddingExcludesLanguage),
        'embeddingExcludesLanguage should be an array of string'
    );
    assert(
        () => typeof config.embeddingMinDistance === 'number' && config.embeddingMinDistance >= 0,
        'embeddingMinDistance should be a positive integer'
    );

    return config;
}

void (async () => {
    try {
        console.log('Starting main process...');
        console.log('Parsing command line arguments...');
        const argv = await yargs(hideBin(process.argv))
            .option('input', {type: 'string', demandOption: true})
            .option('cwd', {type: 'string', demandOption: false})
            .option('output', {type: 'string', demandOption: false})
            .option('config', {type: 'string', demandOption: false})
            .alias('i', 'input')
            .alias('d', 'cwd')
            .alias('o', 'output')
            .alias('c', 'config')
            .parse();

        console.log('Reading configuration...');
        const config = await readConfig(argv.config);
        console.log('Configuration loaded:', config);

        console.log('Reading input file...');
        let input: Input;
        try {
            const inputContent = await fs.readFile(argv.input, 'utf-8');
            input = JSON.parse(inputContent);
            console.log('Input loaded:', {
                queryLength: input.query.length,
                hasEmbeddingRepoId: !!input.embeddingRepoId,
                hasOutput: !!input.output,
                hasEnvironment: !!input.environment
            });
        } catch (error) {
            console.error('Error reading or parsing input file:', error);
            throw error;
        }

        input.environment = input.environment ?? process.env.COMPOSER_ENVIRONMENT ?? '';

        if (!input.query.trim()) {
            throw new Error(`You must specify a user query in ${argv.input}.`);
        }

        const output = input.output ?? argv.output;

        if (!output) {
            throw new Error(`You must specify an output file either in ${argv.input} or in --output option.`);
        }

        const cwd = argv.cwd ?? path.dirname(argv.input);
        console.log('Changing working directory to:', cwd);
        process.chdir(cwd);
        console.log('Current working directory:', process.cwd());

        const outputDir = path.dirname(path.dirname(argv.input));

        console.log('Preparing worker options...');
        const options: WorkerOptions = {
            globalConfig: config,
            output: path.resolve(outputDir, output),
            embeddingRepoId: input.embeddingRepoId ?? null,
            environment: input.environment,
        };
        console.log('Worker options:', JSON.stringify(options, null, 2));

        console.log('Creating worker instance...');
        const worker = new Worker(options);
        const request: ComposerRequest = {
            uuid: newUuid(),
            threadUuid: newUuid(),
            body: {
                type: 'text',
                content: input.query,
            },
        };
        console.log('Request:', JSON.stringify(request, null, 2));

        console.log('Starting request handling...');
        await worker.handleRequest(request);
        console.log('Request handling completed successfully');
    } catch (error) {
        console.error('Fatal error in main process:', error);
        process.exit(1);
    }
})();
