
import {FindFilesByRegExpParameter} from '../../tool';
import {stringifyError} from '../../utils/error';
import {resultMarkdown, ToolImplementBase, ToolRunResult} from './utils';

export class GrepFilesToolImplement extends ToolImplementBase<FindFilesByRegExpParameter> {
    constructor() {
        super({
            type: 'object',
            properties: {
                regex: {type: 'string', description: 'File content regex to search'},
                filePathGlobPattern: {type: 'string', description: 'File path regex to filter files'}
            },
            required: ['regex']
        });
    }

    protected parseArgs(args: Record<string, string>): FindFilesByRegExpParameter {
        return {
            regex: args.regex || '.*',
            filePathGlobPattern: args.filePathGlobPattern || '*'
        };
    }

    protected async execute(args: FindFilesByRegExpParameter): Promise<ToolRunResult> {
        // 参数验证
        if (!args.regex || !args.filePathGlobPattern) {
            const errorMsg = 'Invalid parameters: regex and filePathGlobPattern are required.';
            console.error(`[GrepFiles] ${errorMsg}`);
            return {
                type: 'validationError',
                message: errorMsg
            };
        }

        const {execa, ExecaError} = await import('execa');
        const cwd = process.cwd();

        try {
            // 规范化路径模式，确保以./开头
            const normalizedPathPattern = args.filePathGlobPattern.startsWith('./')
                ? args.filePathGlobPattern
                : `./${args.filePathGlobPattern}`;

            console.log(`[GrepFiles] Starting search with filePathGlobPattern: ${normalizedPathPattern} (original: ${args.filePathGlobPattern}), regex: ${args.regex}`);

            // 使用 find 和 grep 命令实现复杂搜索
            const findResult = await execa(
                'find',
                [
                    '.',
                    '-type', 'f',
                    '-path', normalizedPathPattern,
                    '!', '-path', '*/.*',
                    '!', '-path', '*/.*/**'
                ],
                {cwd, reject: false}  // 不抛出错误，返回错误状态
            );

            if (findResult.exitCode !== 0) {
                const errorMsg = `Find command failed with exit code ${findResult.exitCode}: ${findResult.stderr}`;
                console.error(`[GrepFiles] ${errorMsg}`);
                return {
                    type: 'executeError',
                    output: errorMsg
                };
            }

            const files = findResult.stdout.trim().split('\n').filter(Boolean);
            console.log(`[GrepFiles] Found ${files.length} files to search`);

            const grepResults: string[] = [];

            for (const file of files) {
                try {
                    const grepResult = await execa(
                        'grep',
                        [
                            '-iHn',
                            '-E', args.regex,
                            '-C', '3',
                            file
                        ],
                        {cwd, reject: false}  // 不抛出错误，返回错误状态
                    );

                    if (grepResult.stdout) {
                        grepResults.push(`=== ${file} ===`);
                        grepResults.push(grepResult.stdout);
                        grepResults.push('');  // 额外换行分隔
                    } else if (grepResult.exitCode !== 0 && grepResult.exitCode !== 1) {
                        console.error(`[GrepFiles] Grep failed for file ${file}: ${grepResult.stderr}`);
                    }
                } catch (ex) {
                    console.error(`[GrepFiles] Unexpected error processing file ${file}:`, ex);
                }
            }

            if (grepResults.length === 0) {
                console.log('[GrepFiles] No matching files or content found');
                let output = 'No matching files or content found.';

                // 添加对大括号表达式的提示
                if (args.filePathGlobPattern.includes('{') && args.filePathGlobPattern.includes('}')) {
                    output += '\n\nNote: Brace expansion patterns like {ts,tx} in filePathGlobPattern are not fully supported and may not work as expected.';
                }

                return {
                    type: 'success',
                    finished: false,
                    output: output
                };
            }

            let output = grepResults.join('\n');
            const MAX_OUTPUT_LENGTH = 6000;
            let truncated = false;

            if (output.length > MAX_OUTPUT_LENGTH) {
                output = output.substring(0, MAX_OUTPUT_LENGTH);
                truncated = true;
                console.log(`[GrepFiles] Output truncated to ${MAX_OUTPUT_LENGTH} characters`);
            }

            let markdownOutput = resultMarkdown('Grep Results', output);
            if (truncated) {
                markdownOutput += '\n\n... (results truncated due to length limit)';
            }

            // 添加对大括号表达式的提示
            if (args.filePathGlobPattern.includes('{') && args.filePathGlobPattern.includes('}')) {
                markdownOutput += '\n\nNote: Brace expansion patterns like {ts,tx} in filePathGlobPattern are not fully supported and may not work as expected.';
            }

            return {
                type: 'success',
                finished: false,
                output: markdownOutput
            };
        } catch (ex) {
            const errorMsg = `Fatal error during grep operation: ${stringifyError(ex)}`;
            console.error(`[GrepFiles] ${errorMsg}`);
            return {
                type: 'executeError',
                output: errorMsg
            };
        }
    }
}
