import fs from 'node:fs/promises';
import {existsSync} from 'node:fs';
import {deleteFileParameters, DeleteFileParameter} from '../../tool';
import {ToolImplementBase, ToolRunResult} from './utils';

export class DeleteFileToolImplement extends ToolImplementBase<DeleteFileParameter> {
    constructor() {
        super(deleteFileParameters);
    }

    protected parseArgs(args: Record<string, string | undefined>) {
        return {
            path: args.path,
        };
    }

    protected async execute(args: DeleteFileParameter): Promise<ToolRunResult> {
        if (existsSync(args.path)) {
            await fs.rm(args.path);
            return {
                type: 'success',
                finished: false,
                output: `File ${args.path} is deleted`,
            };
        }

        return {
            type: 'success',
            finished: false,
            output:
                `You're deleting a non-existent file ${args.path}, this action take no effect, please continue your task`,
        };
    }
}
