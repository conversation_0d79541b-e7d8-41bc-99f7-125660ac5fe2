function calculateSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1;
    if (str1.length === 0 || str2.length === 0) return 0;

    const matrix: number[][] = Array(str1.length + 1)
        .fill(null)
        .map(() => Array(str2.length + 1).fill(0));

    for (let i = 0; i <= str1.length; i++) matrix[i][0] = i;
    for (let j = 0; j <= str2.length; j++) matrix[0][j] = j;

    for (let i = 1; i <= str1.length; i++) {
        for (let j = 1; j <= str2.length; j++) {
            const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
            matrix[i][j] = Math.min(
                matrix[i - 1][j] + 1,
                matrix[i][j - 1] + 1,
                matrix[i - 1][j - 1] + cost
            );
        }
    }

    const maxLength = Math.max(str1.length, str2.length);
    return 1 - matrix[str1.length][str2.length] / maxLength;
}

export interface FuzzyMatchResult {
    content: string[];
    similarity: number;
    startIndex: number;
}

export function findMostSimilarContent(
    source: string[],
    target: string[],
    startSearchFrom: number = 0
): FuzzyMatchResult {
    const searchArea = source.slice(startSearchFrom);
    let bestMatch: FuzzyMatchResult = {
        content: [],
        similarity: 0,
        startIndex: -1
    };

    // Convert arrays to strings for comparison
    const targetStr = target.join('\n');

    for (let i = 0; i <= searchArea.length - target.length; i++) {
        const candidateLines = searchArea.slice(i, i + target.length);
        const candidateStr = candidateLines.join('\n');
        const similarity = calculateSimilarity(targetStr, candidateStr);

        if (similarity > bestMatch.similarity) {
            bestMatch = {
                content: candidateLines,
                similarity: similarity,
                startIndex: i + startSearchFrom
            };
        }
    }

    return bestMatch;
}
