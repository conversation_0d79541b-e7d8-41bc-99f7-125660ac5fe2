import {
    AssistantTextMessage,
    deserializeMessage,
    Message,
    MessageData,
    ToolCallMessage,
    ToolCallMessageData,
} from './message';

export type WorkflowOriginMessage = ToolCallMessage;

/** 可用于触发工作流的消息类型，当前只有工具调用会触发 */
export type WorkflowOriginMessageData = ToolCallMessageData;

/** 工作流数据，一个工作流是从模型返回工具调用开始，包含若干次参数检验、修复过程，最终返回调用结果给模型的过程 */
export interface WorkflowData {
    /** 模型发起的调用消息 */
    origin: WorkflowOriginMessageData;
    /** 过程数据，包括参数修复、调用结果等 */
    reactions: MessageData[];
    /** 其中只有部分消息需要最终暴露给下一轮模型调用 */
    exposed: string[];
}

export class Workflow {
    static from(data: WorkflowData): Workflow {
        const workflow = new Workflow(ToolCallMessage.from(data.origin));
        for (const reaction of data.reactions) {
            workflow.addReaction(deserializeMessage(reaction), false);
        }
        for (const uuid of data.exposed) {
            workflow.exposeMessage(uuid);
        }
        return workflow;
    }

    private continueRoundtrip = false;

    private readonly origin: WorkflowOriginMessage;

    private readonly reactions: Message[] = [];

    private readonly exposed: string[] = [];

    constructor(origin: WorkflowOriginMessage) {
        this.origin = origin;
    }

    shouldContinueRoundtrip() {
        return this.continueRoundtrip;
    }

    setContinueRoundtrip(shouldContinue: boolean) {
        this.continueRoundtrip = shouldContinue;
    }

    startReaction(uuid: string) {
        const message = new AssistantTextMessage(uuid);
        this.reactions.push(message);
        return message;
    }

    exposeMessage(messageUuid: string) {
        const message = this.findMessageStrict(messageUuid);
        this.exposed.push(message.uuid);
    }

    addReaction(message: Message, exposed: boolean) {
        this.reactions.push(message);
        if (exposed) {
            this.exposed.push(message.uuid);
        }
    }

    isOriginatedBy(uuid: string) {
        return this.origin.uuid === uuid;
    }

    hasMessage(messageUuid: string) {
        if (this.origin.uuid === messageUuid) {
            return true;
        }
        return this.reactions.some(v => v.uuid === messageUuid);
    }

    findMessage(messageUuid: string) {
        if (this.origin.uuid === messageUuid) {
            return this.origin;
        }

        const message = this.reactions.find(v => v.uuid === messageUuid);
        return message ?? null;
    }

    toMessages() {
        return [
            this.origin,
            ...this.reactions.filter(v => this.exposed.includes(v.uuid)),
        ];
    }

    toWorkflowData(): WorkflowData {
        return {
            origin: this.origin.toMessageData(),
            reactions: this.reactions.map(v => v.toMessageData()),
            exposed: this.exposed,
        };
    }

    private findMessageStrict(messageUuid: string) {
        const message = this.findMessage(messageUuid);

        if (!message) {
            throw new Error(`Message ${messageUuid} not found in workflow`);
        }

        return message;
    }
}
