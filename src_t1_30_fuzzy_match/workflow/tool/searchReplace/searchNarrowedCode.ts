/** 成功定位后的结果 */
export interface NarrowBlockResult {
    /** 起始行号（0-based，包含） */
    start: number;
    /** 结束行号（0-based，**不含**） */
    end: number;
    /** 提取出的完整代码块（以 \n 拼接） */
    block: string;
}

const normalize = (input: string | undefined) => (typeof input === 'string' ? input : '');

/**
 * 根据“从头/从尾逐行收窄”的策略，在 `content` 中定位与 `snippet`
 * 最相关的连续区段。
 *
 * - 当 `snippet` 行数 ≤ 2 时直接返回 `null`（规则失效）。
 * - 若首行或末行无法命中，也返回 `null`。
 */
export function searchNarrowedCode(content: string, snippet: string): NarrowBlockResult | null {
    const fileLines = content.split(/\r?\n/);
    const snipLines = snippet.split(/\r?\n/);

    // 片段行数不足，无搜索意义
    if (snipLines.length <= 2) {
        return null;
    }

    /* ---------- ① 由头向后收窄 ---------- */
    let candidates: number[] = fileLines.map((_, i) => i);
    let matchedHead = 0;

    for (const line of snipLines) {
        const hits = candidates.filter(
            idx => normalize(fileLines[idx]) === normalize(line)
        );

        if (hits.length === 0) {
            if (matchedHead === 0) {
                // 首行就没匹配：无解
                return null;
            }
            break;
        }

        // 下一轮候选行号应当位于“本轮命中行号 + 1”
        candidates = hits.map(idx => idx + 1);
        matchedHead++;
    }

    // 根据最后一轮候选行号推导片段起始行
    const start = Math.max(...candidates) - matchedHead;

    /* ---------- ② 由尾向前收窄 ---------- */

    // 仅在 [start, …] 范围内继续向前收窄，减少搜索量
    candidates = fileLines
        .slice(start)
        .map((_, i) => start + i); // 重新生成行号（绝对）

    let matchedTail = 0; // 成功反向匹配的行数

    for (const line of [...snipLines].reverse()) {
        const hits = candidates.filter(
            idx => normalize(fileLines[idx]) === normalize(line)
        );

        if (hits.length === 0) {
            // 末行完全不匹配：无解
            if (matchedTail === 0) {
                return null;
            }
            break;
        }

        // 下一轮候选行号应当位于“本轮命中行号 - 1”
        candidates = hits.map(idx => idx - 1);
        matchedTail++;
    }

    // 根据第一轮命中行号推导片段结束行（end 不包含）
    const end = Math.min(...candidates) + matchedTail + 1;

    /* ---------- ③ 结果返回 ---------- */
    return {
        start,
        end,
        block: fileLines.slice(start, end).join('\n'),
    };
}
