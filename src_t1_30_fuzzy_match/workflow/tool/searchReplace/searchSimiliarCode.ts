import {stringSimilarity} from 'string-similarity-js';

/** 允许调用方自定义的可选参数 */
export interface FindClosestBlockOptions {
    /** 锚点行与目标首行之间允许的最大行距（默认 4） */
    maxLineGap?: number;
    /** 接受匹配结果的最低相似度阈值（默认 0.92） */
    threshold?: number;
}

/** 匹配结果 */
export interface MatchResult {
    /** 代码块在原文件中起止行号（1 基） */
    start: number;
    end: number;
    /** `stringSimilarity` 计算得到的分数 ∈ [0, 1] */
    score: number;
    /** 实际匹配到的代码片段 */
    block: string;
}

/** 统一行尾并拆分为行数组 */
const splitLines = (text: string): string[] => text.replace(/\r\n?/g, '\n').split('\n');

/** 归一化行内容：去除两侧空白并保留内部缩进 */
const normalize = (line: string): string => line.trim();

/** 构建 “行内容 → 所有出现行号” 的索引表 */
const buildLineIndex = (lines: string[]): Map<string, number[]> => {
    const map = new Map<string, number[]>();
    lines.forEach((line, idx) => {
        const key = normalize(line);
        // 跳过空白行
        if (!key) {
            return;
        }
        if (!map.has(key)) {
            map.set(key, []);
        }
        map.get(key)!.push(idx);
    });
    return map;
};

/** 从目标代码中寻找第一个可用锚点行，返回可能的窗口起点 */
const collectCandidateWindows = (
    fileIndex: Map<string, number[]>,
    targetLines: string[],
    maxLineGap: number
): number[] => {
    for (let j = 0; j < targetLines.length; j++) {
        const key = normalize(targetLines[j]);
        if (!key) {
            continue;
        }

        const occurrences = fileIndex.get(key);
        if (!occurrences) {
            continue;
        }

        // 对每个命中行号，根据 j 推导窗口起点
        return occurrences.map(idx => Math.max(0, idx - maxLineGap - j));
    }
    // 若所有行均未命中，回退到整文件搜索
    return [0];
};

/* ---------- 主函数 ---------- */

/**
 * 在给定代码文本 `content` 中查找与 `targetCode` 最相似的代码块。
 * @returns  当找到相似度 ≥ threshold 的片段时返回其信息；否则返回 null。
 */
export function searchSimiliarCode(content: string, targetCode: string, {
    maxLineGap = 4,
    threshold = 0.92,
}: FindClosestBlockOptions = {}): MatchResult | null {
    const fileLines = splitLines(content);
    const targetLines = splitLines(targetCode);
    const targetLen = targetLines.length;

    // ① 构建行索引
    const lineIndex = buildLineIndex(fileLines);

    // ② 基于首个锚点行收集候选窗口
    const candidateStarts = collectCandidateWindows(lineIndex, targetLines, maxLineGap);

    // ③ 在每个候选窗口内滑动比较
    let best: MatchResult | null = null;

    for (const windowStart of candidateStarts) {
        const windowEnd = Math.min(fileLines.length, windowStart + targetLen + maxLineGap);

        for (let i = windowStart; i <= windowEnd - targetLen; i++) {
            // 允许窗口向后扩张，以覆盖“缺行 / 多行” 的情况
            const maxWidth = Math.min(windowEnd - i, targetLen + maxLineGap);

            for (let w = targetLen; w <= maxWidth; w++) {
                const block = fileLines.slice(i, i + w).join('\n');
                const score = stringSimilarity(targetCode, block);

                // eslint-disable-next-line max-depth
                if (!best || score > best.score) {
                    best = {
                        start: i + 1, // 转为 1 基行号
                        end: i + w,
                        score,
                        block,
                    };

                    // 已经非常接近，提前结束
                    // eslint-disable-next-line max-depth
                    if (score >= 0.999) {
                        return best;
                    }
                }
            }
        }
    }

    // 最终结果判断阈值
    if (best && best.score >= threshold) {
        return best;
    }
    return null;
}
