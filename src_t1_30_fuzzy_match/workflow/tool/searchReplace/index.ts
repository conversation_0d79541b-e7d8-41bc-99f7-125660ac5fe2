/* eslint-disable max-len */
import {stringSimilarity} from 'string-similarity-js';
import flow from 'lodash/flow.js';
import {searchNarrowedCode} from './searchNarrowedCode.js';
import {searchSimiliarCode} from './searchSimiliarCode.js';

export interface ParsedPatch {
    search: string[];
    replace: string[];
}

interface ParseState {
    context: 'none' | 'search' | 'replace';
    current: ParsedPatch | null;
}

export class PatchFileDelimiterError extends Error {
    message = 'Delimiter error for patch format！Delimiters should be one of the following:'
        + '"\n<<< SEARCH <<<\n", "\n==== REPLACED_BY ====\n", "\n>>> END >>>\n"';
}

// eslint-disable-next-line complexity
function parsePatchString(input: string): ParsedPatch[] {
    // A patch block looks like this:
    //
    // ```
    // <<<SEARCH<<<
    // content to search
    // ====REPLACED_BY====
    // content to replace
    // >>>END>>>
    // ```
    //
    // For more stable parsing, we allow `<<<<`, `====` and `>>>>` to have different repetive length,
    // but it requires at least 4 in order not to be confused with the `<<`, `>>` or `===` operators
    const results: ParsedPatch[] = [];
    const state: ParseState = {context: 'none', current: null};

    for (const line of input.split('\n')) {
        if (/^[<=]+\s*SEARCH\s*[<=#]+$/.test(line)) {
            if (state.context === 'replace' || state.context === 'search') {
                throw new PatchFileDelimiterError();
            }

            if (state.current && state.context === 'none') {
                results.push(state.current);
            }

            state.context = 'search';
            state.current = {search: [], replace: []};
        }
        else if (/^[<=>]+\s*REPLACED?_BY\s*[<=>]+$/.test(line)) {
            if (state.context !== 'search') {
                throw new PatchFileDelimiterError();
            }
            state.context = 'replace';
        }
        else if (/^[>=]+\s*END\s*[>=]+$/.test(line)) {
            if (state.context !== 'replace') {
                throw new PatchFileDelimiterError();
            }
            state.context = 'none';
        }
        else if (state.context === 'search' && state.current) {
            state.current.search.push(line);
        }
        else if (state.context === 'replace' && state.current) {
            state.current.replace.push(line);
        }
    }

    if (state.current) {
        results.push(state.current);
    }

    return results;
}

interface FlowInput {
    error?: SearchReplacePatchError;
    patchedContent: string;
}

interface Opts {
    /** 是否流式，流式过程中只会做简单的搜索替换，结束或会做更详细的相似度匹配 */
    stream?: boolean;
}

export class SearchReplacePatchError extends Error {
    constructor(readonly patchIndex: number, readonly patches: ParsedPatch[], readonly content: string) {
        super();
    }

    get result() {
        const currentPatch = this.patches[this.patchIndex];
        const matcher = currentPatch.search.join('\n');
        if (matcher.length > 20) {
            return {
                similiarBlock: [],
                failedPatchIndex: this.patchIndex,
                patches: this.patches,
            };
        }

        const result = searchSimiliarCode(this.content, matcher, {threshold: 0, maxLineGap: 0});
        // 扩写为上下两行
        const similiarBlock = result
            ? this.content.split(/\r?\n/).slice(
                Math.max(0, result.start - 2),
                result.end + 2
            )
            : [];
        return {
            /** 失败的补丁序号，从0开始，如果全成功了就是-1 */
            failedPatchIndex: this.patchIndex,
            /** 解析出来的所有补丁 */
            patches: this.patches,
            /** 当前补丁没匹配上，与其最相关的代码块 */
            similiarBlock,
        };
    }
}

const trim = (input: string) => input.replace(/^(\r?\n)+/, '');
export const linesIndexOf = (search: string[], lines: string[]) => {
    let candidates = Array.from(new Array(lines.length)).map((_, i) => i);
    let step = 0;
    while (candidates.length) {
        // eslint-disable-next-line
        candidates = candidates.filter(idx => lines[idx] === search[step]).map(i => i + 1);
        // 只有匹配全部行时，才认为匹配
        if (candidates.length === 1 && step === search.length - 1) {
            return candidates[0] - step - 1;
        }

        if (candidates.length === 0) {
            return -1;
        }

        step = step + 1;
    }
    return -1;
};
const detectLineEnding = (text: string) => {
    const rn = text.indexOf('\r\n');
    const n = text.indexOf('\n');
    const r = text.indexOf('\r');

    if (rn !== -1) {
        return '\r\n'; // Windows风格
    }
    if (n !== -1) {
        return '\n'; // Unix/Linux/macOS风格
    }
    if (r !== -1) {
        return '\r'; // 旧的Mac风格(罕见)
    }
    return '\n'; // 默认
};

function joinLines(start: string[], middle: string[], end: string[], eol: string) {
    if (!middle.toString()) {
        return [...start, ...end].join(eol);
    }

    return [...start, ...middle, ...end].join(eol);
}
/**
 * 1. 去除检索时，模型多生成行首换行
 * 2. 增加模型生成时，行不完整的匹配
 * 3. 增加通过匹配首尾行收窄范围的匹配
 * 4. 增加相似度 n+4 行的相似度匹配
 * 5. 以上策略均为匹配时，返回修改失败，并提供最相似代码上下2行代码，进行二次修改
 */
export const applySearchReplaceChunk = (originalContent: string, searchReplaceChunk: string, opts?: Opts) => {
    try {
        const patches = parsePatchString(searchReplaceChunk);
        const eol = detectLineEnding(originalContent);

        if (!patches.length && !opts?.stream) {
            return {
                error: new PatchFileDelimiterError(),
                patchedContent: originalContent,
                patches: [],
            };
        }

        const {streamLineCount, patchedContent, error} = flow(
            patches.map(
                // eslint-disable-next-line max-statements, complexity
                (patch, i) => (input: FlowInput): FlowInput & {streamLineCount?: number} => {
                    if (input.error) {
                        return input;
                    }

                    const normalizedSearchChunk = trim(patch.search.join(eol));
                    const normalizedSearchChunkLines = normalizedSearchChunk.split(/\r?\n/);
                    const replaceChunkLines = patch.replace;
                    const patchedContent = input.patchedContent;
                    if (opts?.stream) {
                        const chunkIndex = patchedContent.indexOf(normalizedSearchChunk);
                        const replaceStart = patchedContent.slice(0, chunkIndex).split(/\r?\n/).length - 1;
                        const end = replaceStart + patch.replace.length;
                        return {
                            streamLineCount: end,
                            patchedContent: patchedContent.replace(
                                normalizedSearchChunk,
                                trim(replaceChunkLines.join(eol))
                            ),
                        };
                    }
                    else {
                        const patchedContentLines = patchedContent.split(/\r?\n/);
                        const chunkIndex = linesIndexOf(normalizedSearchChunkLines, patchedContentLines);
                        if (chunkIndex !== -1) {
                            const replaceStart = chunkIndex;
                            const replaceEnd = replaceStart + normalizedSearchChunkLines.length;
                            // eslint-disable-next-line max-depth
                            if (!replaceChunkLines.toString()) {
                                return {
                                    patchedContent: joinLines(
                                        patchedContentLines.slice(0, replaceStart),
                                        [],
                                        patchedContentLines.slice(replaceEnd),
                                        eol
                                    ),
                                };
                            }

                            return {
                                patchedContent: joinLines(
                                    patchedContentLines.slice(0, replaceStart),
                                    replaceChunkLines,
                                    patchedContentLines.slice(replaceEnd),
                                    eol
                                ),
                            };
                        }
                        const codeSearched = searchNarrowedCode(patchedContent, normalizedSearchChunk);
                        if (codeSearched) {
                            const score = stringSimilarity(normalizedSearchChunk, codeSearched.block);
                            if (score > 0.67) {
                                return {
                                    patchedContent: joinLines(
                                        patchedContentLines.slice(0, codeSearched.start - 1),
                                        replaceChunkLines,
                                        patchedContentLines.slice(codeSearched.end),
                                        eol
                                    ),
                                };
                            }
                        }

                        if (patchedContentLines.length <= 1000 && normalizedSearchChunkLines.length <= 20) {
                            const similiarCode = searchSimiliarCode(patchedContent, normalizedSearchChunk, {
                                maxLineGap: 4,
                                threshold: 0.86,
                            });
                            if (similiarCode) {
                                return {
                                    patchedContent: joinLines(
                                        patchedContentLines.slice(0, similiarCode.start - 1),
                                        replaceChunkLines,
                                        patchedContentLines.slice(similiarCode.end),
                                        eol
                                    ),
                                };
                            }
                        }

                        return {
                            error: new SearchReplacePatchError(i, patches, originalContent),
                            patchedContent: input.patchedContent,
                        };
                    }
                }
            )
        )({patchedContent: originalContent, streamLineCount: 0});

        return {
            error,
            patchedContent: opts?.stream
                ? patchedContent.split(/\r?\n/).slice(0, streamLineCount).join(eol)
                : patchedContent,
            patches,
        };
    }
    catch (ex) {
        return {
            error: ex,
            patchedContent: originalContent,
            patches: [],
        };
    }
};
