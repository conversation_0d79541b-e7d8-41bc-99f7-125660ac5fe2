# Composer测试工具

本工具实现了一套简化版的模型工具调用，不依赖IDE的接口，同时对接Embedding检索。

具体支持的工具查看[definition.ts](src/tool/definition.ts)文件。

## 使用步骤

前置依赖：

- macOS系统，**不支持Windows和Linux系统**。
- NodeJS 20.17.17以上版本，**建议使用双数版本**。

检查NodeJS和NPM版本：

```shell
node -v # 要求20.17.17以上
npm -v # 要求10以上
```

安装依赖：

```shell
npm install
npm install -g tsx
```

配置环境变量，在当前工具的项目目录创建`.env`文件，内容如下：

```text
API_KEY=xxx
MODEL_NAME=anthropic/claude-3.7-sonnet
```

其中`API_KEY`线下提供。

运行：

```shell
tsx src/main.ts --config=[全局配置文件] --cwd=[测试的项目目录] --input=[用例输入JSON] --output=[输出JSON文件]
```

各参数说明：

- `--input`：**必选参数**，用例输入JSON，格式参考下文，每个用例有自己的输入，建议放在测试的项目下。
- `--config`：全局配置文件，指定一些内部的处理行为，所有用例共享同样的配置，建议就使用本代码库里的`default-config.json`，即`--config=default-config.json`。如不提供该参数，**不会使用`default-config.json`**，而会使用内部写死的默认配置。
- `--cwd`：测试的项目目录，运行时会把当前工作目录切换到这个参数指定的位置。如不提供该参数，使用`input`指定的文件所在的目录。
- `--output`：输出JSON文件，会将测试结果写入这个文件中，以测试项目为基础的相对路径。也可以用`input`指定的JSON文件中的`output`字段指定。

### 推荐用法

将`input`对应的文件放在测试项目根目录下，在`input`指定的JSON中写`output`字段，然后只指定`input`和`config`参数，典型用法：

```shell
tsx src/main.ts \
    --config=config.json \
    --input=/Users/<USER>/xxx-project/case-1.json \
```

配合`case-1.json`中写如下内容：

```json
{
  "query": "为xxx函数编写单元测试",
  "embeddingRepoId": "xxx",
  "output": "case-1-output.json"
}
```

以上使用，会有以下效果：

- 读取这个工具所在目录的`config.json`作为全局配置。
- 把`/Users/<USER>/xxx-project`作为测试的工作目录。
- 读取`/Users/<USER>/xxx-project/case-1.json`文件作为用例。
- 把测试输出的数据写入到`/Users/<USER>/xxx-project/case-1-output.json`文件里。

## 配置

### 全局配置

参考[default-config.json](./default-config.json)文件，配置结构如下：

```ts
interface GlobalConfig {
    /** `distance`小于该距离的丢弃 */
    embeddingMinDistance: number;
    /** `language`在数组中的丢弃，如`['markdown']`可以排除所有MD的召回 */
    embeddingExcludesLanguage: string[];
    /** 加入上下文的类型，`chunk`仅包含片段、`fullContent`包含文件全部内容，`nameOnly`只给文件名 */
    embeddingConetxtMode: EmbeddingContextMode;
}
```

### 用例配置

配置结构如下：

```ts
interface Input {
    /** 用户发起的消息内容 */
    query: string;
    /** Embedding接口的repoId参数 */
    embeddingRepoId?: string;
    /** 对应`output`参数，可以用当前项目为基础的相对路径 */
    output?: string;
}
```

其中`embeddingRepoId`未提供时，不会调用检索服务。该参数的获取方法：

1. 安装Comate度厂版（内部版）插件。
2. 用VSCode打开测试用的项目。
3. 在“Output”面板中，找到“Baidu Comate”的输出。
4. 搜索`repoId`关键字，就能拿到当前项目的`repoId`参数。

## 常见错误

- `Require xxx to create a model client`：环境变量没配全。
- `You must specify ...`：配置文件有问题或者给的命令行参数不对。
- `Unable to apply diff action to file ...`：模型生成的Diff有问题，无法和原文件合并。
- `Invalid tool call message ...`：模型生成的XML格式有问题。
- `Unexpected xxx chunk comining ...`：模型生成的XML格式有问题。
- `Embedding search error: ...`：调用Embedding检索服务出错。
- `Cannot fix tool call without a determined tool name`：模型调用工具时没有工具名称。

## 调整Prompt

Prompt的全部逻辑都在`src/prompt`目录下，以[index.ts](src/prompt/index.ts)为入口，是比较简单的TypeScript代码，多数用数组拼接字符串，其中看到`dedent`函数的调用无需在意，`dedent`的作用是把多行字符串最前面的缩进去掉，这样在代码里对齐比较好看，输出时也不会每行头里多出来一堆空格。

修改完Prompt的逻辑后，必须重新执行工具。**在执行过程中修改代码是没用的。**

## 输出格式

在调用过程中，每条消息都会输出在控制台上，且最后消息记录会在`--output`参数指定的JSON文件中，这个文件的结构大致如下：

输出的结构相对比较复杂，具体请参考以下TypeScript类型定义，写入到JSON文件中的最终结构为最后面的`MessageThreadPersistData`类型，对结构有任何疑问请直接沟通：

```ts
/** 文本块 */
interface TextMessageChunk {
    type: 'text';
    content: string;
}

/** 纯文本块，和`TextMessageChunk`的区别是这个块不会按Markdown处理 */
interface PlainTextMessageChunk {
    type: 'plainText';
    content: string;
}

/** 模块返回的工具调用块 */
interface ToolCallMessageChunk {
    type: 'toolCall';
    toolName: ToolName;
    arguments: Record<string, string>;
    source: string;
}

/** 工具的思维链块 */
interface ThinkingMessageChunk {
    type: 'thinking';
    content: string;
}

/** 可用于普通消息的块 */
type MessageContentChunk = TextMessageChunk | ToolCallMessageChunk | ThinkingMessageChunk;

/** 用于调度消息的块 */
type DebugContentChunk = TextMessageChunk | PlainTextMessageChunk;

/** 一条消息的基础数据 */
interface MessageDataBase {
    uuid: string;
    createdAt: string;
    error?: string | undefined;
}

/** 调试级别 */
type DebugMessageLevel = 'error' | 'warning' | 'info';

/** 调试消息的数据 */
interface DebugMessageData extends MessageDataBase {
    type: 'debug';
    level: DebugMessageLevel;
    title: string;
    content: DebugContentChunk;
}

/** 用户发起的消息的数据 */
interface UserRequestMessageData extends MessageDataBase {
    type: 'userRequest';
    content: string;
}

/** 模型返回的纯文本（无工具调用）消息的数据 */
interface AssistantTextMessageData extends MessageDataBase {
    type: 'assistantText';
    chunks: MessageContentChunk[];
}

/** 模型返回的带有工具调用的消息的数据 */
interface ToolCallMessageData extends MessageDataBase {
    type: 'toolCall';
    chunks: MessageContentChunk[];
}

/** 工具调用后返回给模型的消息数据 */
interface ToolUseMessageData extends MessageDataBase {
    type: 'toolUse';
    content: string;
}

/** 全部消息的数据类型 */
type MessageData =
    | DebugMessageData
    | UserRequestMessageData
    | AssistantTextMessageData
    | ToolCallMessageData
    | ToolUseMessageData;

/** 可用于触发工作流的消息类型，当前只有工具调用会触发 */
type WorkflowOriginMessageData = ToolCallMessageData;

/** 工作流数据，一个工作流是从模型返回工具调用开始，包含若干次参数检验、修复过程，最终返回调用结果给模型的过程 */
interface WorkflowData {
    /** 模型发起的调用消息 */
    origin: WorkflowOriginMessageData;
    /** 过程数据，包括参数修复、调用结果等 */
    reactions: MessageData[];
    /** 其中只有部分消息需要最终暴露给下一轮模型调用 */
    exposed: string[];
}

/** 仅返回纯文本的响应数据 */
interface RoundtripMessageResponseData {
    type: 'message';
    message: AssistantTextMessageData;
}

/** 包含工作流的响应数据 */
interface RoundtripWorkflowResponseData {
    type: 'workflow';
    workflow: WorkflowData;
}

/** 包含调试信息的响应数据 */
interface RoundtripDebugResponseData {
    type: 'debug';
    message: DebugMessageData;
}

/** 一轮对话的响应数据 */
type RoundtripResponseData = RoundtripMessageResponseData | RoundtripWorkflowResponseData | RoundtripDebugResponseData;

/** 一轮对话的数据，包含一个用户发起的请求，和若干个响应 */
interface RoundtripData {
    request: UserRequestMessageData;
    responses: RoundtripResponseData[];
}

/** 一个完整对话（包含多轮用户消息）的数据 */
interface MessageThreadPersistData {
    uuid: string;
    roundtrips: RoundtripData[];
}
```
