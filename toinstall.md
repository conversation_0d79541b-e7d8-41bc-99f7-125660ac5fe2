# for mac and linux


# python version :
# Python 3.9.6

# 安装 Homebrew（如果还没装）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安装 Node.js（包含 npm）
brew install node

# 安装 pnpm 和 yarn（通过 npm）
npm install -g pnpm
npm install -g yarn
npm install --save-dev dotenv
npm install --save-dev wireit

# 安装 Maven
brew install maven

# 安装 Go
brew install go

# 安装 Java（默认版本）
brew install openjdk

# 配置 Java 环境变量（可选）
sudo ln -sfn $(brew --prefix openjdk)/libexec/openjdk.jdk /Library/Java/JavaVirtualMachines/openjdk.jdk
echo 'export PATH="/opt/homebrew/opt/openjdk/bin:$PATH"' >> ~/.zprofile
source ~/.zprofile


# for linux

# 更新包管理器
sudo apt update

# 安装 Python（默认版本 3）
sudo apt install -y python3 python3-pip

# 安装 Node.js 和 npm（官方源）
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# 安装 pnpm 和 yarn（通过 npm）
npm install -g pnpm
npm install -g yarn
npm install --save-dev dotenv
npm install --save-dev wireit

# 安装 Maven
sudo apt install -y maven

# 安装 Go
sudo apt install -y golang

# 安装 Java（OpenJDK）
sudo apt install -y openjdk-17-jdk

# 安装 bc
sudo apt install -y bc


# run example on mac:

# for openrouter api:
# you need to put

API_KEY={your openrouter api key here}
MODEL_NAME=anthropic/claude-3.7-sonnet
# into your .env file as default value

nohup caffeinate -i python auto_eval/run_tests.py --run_instance_id trial0 --res_path auto_eval/std10_src_t1_30_kimik2_3 --model_type claude --model_name moonshotai/kimi-k2 --eval_list_path auto_eval/eval_list_full.json --strategy src_t1_30 > std10_src_t1_30_kimik2_3.log 2>&1 &
    # where std10_src_t1_30_kimik2_3  is the result folder, the "_3" at the end is the repetition index, should be 1, 2, 3. each experiment should run 3 times to get the average score.
    # --strategy src_t1_30 means the strategy is src_t1_30, this is the baseline strategy corresponding to the cli project src_t1_30, other cli project like src_t1_30_fuzzy_match and src_t1_30_pimp are variation of the base strategy, you can copy src_t1_30 to another to make your own strategy.
    # --eval_list_path auto_eval/eval_list_full.json is the list of cases to be run, eval_list_full.json  is  the full list of test case, to be update when new cases coming.
    # --model_type claude means the model api provider is openrouter, other model type like huoshan_ds means the model api provider is huoshan
    # --model_name moonshotai/kimi-k2 is the model name, this is one model name for openrouter api, other model name like deepseek-v3-250324 is the model name for huoshan api

# for huoshan api:
nohup caffeinate -i python auto_eval/run_tests.py --run_instance_id trial0 --res_path auto_eval/std10_src_t1_30_limit_2 --model_type huoshan_ds --model_name deepseek-v3-250324 --eval_list_path auto_eval/eval_list_full.json --strategy src_t1_30 --local_ds_api_key {your huoshan api key here} > std10_src_t1_30_limit_2.log 2>&1 &
   # where --local_ds_api_key {your huoshan api key here} is the api key for huoshan api. this will override your default key in .env file



