interface RootEntriesMetadata {
    totalCount: number;
    truncatedCount: number;
}

export function renderRootEntriesSection(entries: string | string[], metadata?: RootEntriesMetadata) {
    const lines = [
        '## Project Structure',
        '',
        'Project files and directories structure (directories end with `/`):',
        '',
    ];

    if (typeof entries === 'string') {
        lines.push(entries);
        if (metadata) {
            lines.push('');
            lines.push(`Total ${metadata.totalCount} entries${metadata.truncatedCount ? `, ${metadata.truncatedCount} entries not shown` : ''}`);
        }
    } else {
        lines.push(...entries.map(v => `- ${v}`));
    }

    return lines.join('\n');
}
