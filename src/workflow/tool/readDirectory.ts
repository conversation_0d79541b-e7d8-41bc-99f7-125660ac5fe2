import {readDirectoryParameters, ReadDirectoryParameter} from '../../tool';
import {stringifyError} from '../../utils/error';
import {resultMarkdown, ToolImplementBase, ToolRunResult} from './utils';

async function walkDirectory(cwd: string, path: string, recursive: boolean, indent: number, output: string[]) {
    const {globby} = await import('globby');
    const entries = await globby(
        path,
        {
            cwd,
            gitignore: true,
            expandDirectories: true,
            absolute: false,
            dot: false,
            markDirectories: true,
            ignore: ['.*', '**/.*'], // Exclude all hidden directories and files at all levels
        }
    );

    const indentString = ' '.repeat(indent * 2);
    for (const entry of entries) {
        output.push(indentString + entry);
        if (entry.endsWith('/') && recursive) {
            await walkDirectory(cwd, entry.slice(0, -1), recursive, indent + 1, output);
        }
    }

    return entries;
}

export class ReadDirectoryToolImplement extends ToolImplementBase<ReadDirectoryParameter> {
    constructor() {
        super(readDirectoryParameters);
    }

    protected parseArgs(args: Record<string, string>): ReadDirectoryParameter {
        return {
            path: args.path,
            recursive: args.recursive === 'true',
        };
    }

    protected async execute(args: ReadDirectoryParameter): Promise<ToolRunResult> {
        const cwd = process.cwd();
        const MAX_LINES = 100; // Maximum number of lines to return
        try {
            const tree: string[] = [];
            await walkDirectory(cwd, args.path, args.recursive ?? false, 0, tree);

            if (!tree.length) {
                return {
                    type: 'success',
                    finished: false,
                    output: `There are no files in directory ${args.path}`,
                };
            }

            const displayedEntries = tree.slice(0, MAX_LINES);
            const remainingEntries = tree.length - displayedEntries.length;
            const output = displayedEntries.join('\n');
            const truncationMessage = remainingEntries > 0 ? `\n\n...${remainingEntries} more entries not shown. You may need to adjust your request to see more.` : '';

            return {
                type: 'success',
                finished: false,
                output: resultMarkdown(
                    `Files in directory ${args.path}, directories are followed by \`/\`: (Hidden files and directories starting with . are excluded)`,
                    output + truncationMessage
                ),
            };
        }
        catch (ex) {
            return {
                type: 'executeError',
                output: `Unable to read directory ${args.path}: ${stringifyError(ex)}`,
            };
        }
    }
}
