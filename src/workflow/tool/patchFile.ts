import fs from 'node:fs/promises';
import {existsSync} from 'node:fs';
import dedent from 'dedent';
import {patchFileParameters, PatchFileParameter} from '../../tool';
import {stringifyError} from '../../utils/error';
import {patchContent, PatchParseError, PatchResult} from '../../utils/patch';
import {resultMarkdown, ToolImplementBase, ToolRunResult} from './utils';
import * as diffLib from 'diff';

/**
 * 使用diff库生成两个字符串之间的差异标记
 * @param oldStr 原始字符串
 * @param newStr 新字符串
 * @returns 带有差异标记的字符串
 */
function diffString(oldStr: string, newStr: string): string {
    const diff = diffLib.diffLines(oldStr, newStr);
    let result = '';

    diff.forEach(part => {
        // 根据diff库的输出格式，标记添加、删除和不变的行
        const prefix = part.added ? '+ ' : part.removed ? '- ' : '  ';
        // 不过滤空行，保留所有行
        const lines = part.value.split('\n');

        // 处理最后一个空行的特殊情况
        if (lines[lines.length - 1] === '') {
            lines.pop();
        }

        lines.forEach(line => {
            result += `${prefix}${line}\n`;
        });
    });

    return result;
}

export class PatchFilesToolImplement extends ToolImplementBase<PatchFileParameter> {
    constructor() {
        super(patchFileParameters);
    }

    protected parseArgs(args: Record<string, string | undefined>) {
        return {
            path: args.path,
            content: args.content,
        };
    }

    protected async execute(args: PatchFileParameter): Promise<ToolRunResult> {
        try {
            if (!existsSync(args.path)) {
                return {
                    type: 'executeError',
                    output:
                        `There is no original ${args.path}, please carefully check the existence and content of file, use \`write_file\` tool if you are creating a new file`,
                };
            }

            const content = await fs.readFile(args.path, 'utf-8');
            const patchResult: PatchResult = patchContent(content, args.content);

            if (patchResult.type === 'success' && patchResult.newContent) {
                await fs.writeFile(args.path, patchResult.newContent);
                return {
                    type: 'success',
                    finished: false,
                    output: resultMarkdown(
                        `Patch is written to ${args.path}, here is the new content of this file:`,
                        patchResult.newContent
                    ),
                };
            } else if (patchResult.type === 'partialSuccess' && patchResult.newContent) {
                // 如果成功计数为0，则视为完全失败而不是部分成功
                if (patchResult.successCount === 0) {
                    const fuzzyMatchInfo = patchResult.fuzzyMatches
                        ?.map(match => {
                            const visualizeWhitespace = (str: string) =>
                                str.replace(/( )/g, ' ');

                            const searchWithVisible = match.searchContent.map(visualizeWhitespace);
                            const matchedWithVisible = match.matchedContent.map(visualizeWhitespace);

                            const diffResult = diffString(
                                match.matchedContent.join('\n'),
                                match.searchContent.join('\n')
                            );

                            return `\nFailed patch with following details:\nSearch content :\n\`\`\`\n${searchWithVisible.join('\n')}\n\`\`\`\n` +
                                   `Most similar existing content :\n\`\`\`\n${matchedWithVisible.join('\n')}\n\`\`\`\n` +
                                   `Diff (- existing, + search):\n\`\`\`diff\n${diffResult}\n\`\`\`\n`;
                        })
                        .join('\n\n') || '';

                    return {
                        type: 'executeError',
                        output: `\nAll patches failed for ${args.path}. Nothing has been applied.\n${fuzzyMatchInfo}\nPlease adjust your search content to match the existing code or use the suggested similar content or decompose large patch into smaller patches.\n`
                    };
                }

                // 处理部分成功的情况
                await fs.writeFile(args.path, patchResult.newContent);
                const fuzzyMatchInfo = patchResult.fuzzyMatches
                    ?.map(match => {
                        const visualizeWhitespace = (str: string) =>
                            str.replace(/( )/g, ' ');

                        const searchWithVisible = match.searchContent.map(visualizeWhitespace);
                        const matchedWithVisible = match.matchedContent.map(visualizeWhitespace);

                        const diffResult = diffString(
                            match.matchedContent.join('\n'),
                            match.searchContent.join('\n')
                        );

                        return `\nFailed patch with following details:\nSearch content :\n\`\`\`\n${searchWithVisible.join('\n')}\n\`\`\`\n` +
                               `Most similar existing content :\n\`\`\`\n${matchedWithVisible.join('\n')}\n\`\`\`\n` +
                               `Diff (- existing, + search):\n\`\`\`diff\n${diffResult}\n\`\`\`\n`;
                    })
                    .join('\n\n') || '';
                return {
                    type: 'success',
                    finished: false,
                    output: `Partially successful patches have been applied to ${args.path}. The first ${patchResult.successCount} out of ${patchResult.totalCount} patches were successful.\n\nHere is the new content:` +
                            `\n\`\`\`\n ${patchResult.newContent}\n\`\`\`\n\n\n The patch ${patchResult.totalCount + 1} failed, you can continue adjusting your query from this patch:\n ${fuzzyMatchInfo}`

                };
            } else if (patchResult.type === 'fuzzyMatch' && patchResult.fuzzyMatches) {
                // 处理模糊匹配情况
                const fuzzyMatchInfo = patchResult.fuzzyMatches
                    .map(match => {
                        // 将空格和制表符替换为可见字符，以便更容易看出差异
                        const visualizeWhitespace = (str: string) =>
                            str.replace(/( )/g, ' ');

                        const searchWithVisible = match.searchContent.map(visualizeWhitespace);
                        const matchedWithVisible = match.matchedContent.map(visualizeWhitespace);

                        // 使用diff库生成差异字符串
                        const diffResult = diffString(
                            match.matchedContent.join('\n'),
                            match.searchContent.join('\n')
                        );

                        return `\nFound similar content with ${(match.similarity * 100).toFixed(2)}% similarity:\n` +
                               `Search content :\n\`\`\`\n${searchWithVisible.join('\n')}\n\`\`\`\n` +
                               `Most similar existing content :\n\`\`\`\n${matchedWithVisible.join('\n')}\n\`\`\`\n` +
                               `Diff (- existing, + search):\n\`\`\`diff\n${diffResult}\n\`\`\`\n`
                    })
                    .join('\n\n');
                return {
                    type: 'executeError',
                    output: `
Failed to find exact match for some patches in ${args.path}. Nothing is written to ${args.path}.
${fuzzyMatchInfo}
Please adjust your search content to match the existing code or use the suggested similar content or decompose large patch into smaller patches.`,
                };
            } else if (patchResult.type === 'parseError') {
                // 处理解析错误
                return {
                    type: 'requireFix',
                    includesBase: true,
                    prompt: dedent`
                        Parse <content> parameter error: ${patchResult.error || 'Unknown error'}.\n\n
                        A patch block always consists a \`SEARCH\` and a \`REPLACE\` part, in format like this:\n\n
                        \`\`\`
                        <<< SEARCH <<<
                        [Exact content to search]
                        === REPLACED_BY ===
                        [New content to replace with]
                        >>> END >>>
                        \`\`\`
                        Please regenerate the <patch_file> tool call with correct patch content.
                    `,
                };
            } else {
                // 未知错误类型
                return {
                    type: 'executeError',
                    output: `Unknown error type encountered while patching file ${args.path}.`,
                };
            }
        } catch (ex) {
            // 处理其他未预期的错误
            return {
                type: 'executeError',
                output: dedent`
                Unexpected error while patching file ${args.path}: ${stringifyError(ex)}
                Please check the file content and your patch instructions.`,
            };
        }
    }
}
