import fs from 'node:fs/promises';
import {MessageThread} from './thread';

export {
    ToolUseMessage,
    ToolCallMessage,
    UserRequestMessage,
    AssistantTextMessage,
    DebugMessage,
    isToolCallChunk,
    isReactiveToolCallChunk,
    isAssistantMessage,
} from './message';
export type {
    ToolCallMessageChunk,
    PlainTextMessageChunk,
    ThinkingMessageChunk,
    MessageContentChunk,
    DebugContentChunk,
    Message,
    MessageData,
    ToolUseMessageData,
    ToolCallMessageData,
    UserRequestMessageData,
    AssistantTextMessageData,
    DebugMessageLevel,
    DebugMessageData,
    MessageType,
} from './message';
export {Roundtrip} from './roundtrip';
export type {RoundtripData} from './roundtrip';
export {MessageThread} from './thread';
export type {MessageThreadData, MessageThreadPersistData} from './thread';
export {Workflow} from './workflow';
export type {WorkflowOriginMessage} from './workflow';

export class ThreadStore {
    private threads: MessageThread[] = [];

    private readonly file: string;

    private tokenUsage = { input_tokens: 0, output_tokens: 0 };

    // 添加 getter 来获取输出文件路径
    get outputPath(): string {
        return this.file;
    }

    // 添加设置 token 使用量的方法
    setTokenUsage(inputTokens: number, outputTokens: number) {
        this.tokenUsage = {
            input_tokens: inputTokens,
            output_tokens: outputTokens
        };
    }

    constructor(file: string) {
        this.file = file;
    }

    ensureThread(threadUuid: string) {
        const thread = this.threads.find(v => v.uuid === threadUuid);

        if (thread) {
            return thread;
        }

        const newThread = new MessageThread(threadUuid);
        this.threads.unshift(newThread);
        return newThread;
    }

    moveThreadToTop(threadUuid: string) {
        const targetThreadIndex = this.threads.findIndex(v => v.uuid === threadUuid);
        if (targetThreadIndex >= 0) {
            const targetThread = this.threads[targetThreadIndex];
            this.threads.splice(targetThreadIndex, 1);
            this.threads.unshift(targetThread);
        }
    }

    findThreadByUuidStrict(threadUuid: string) {
        const thread = this.threads.find(v => v.uuid === threadUuid);

        if (thread) {
            return thread;
        }

        throw new Error(`Thread ${threadUuid} not found`);
    }

    async persist(): Promise<void> {
        const data = this.threads.map(v => ({
            ...v.toPersistData(),
            input_tokens: this.tokenUsage.input_tokens,
            output_tokens: this.tokenUsage.output_tokens
        }));

        // 使用 JSON.stringify 后，再将 Unicode 转义序列（\uXXXX）替换为实际字符
        // 这样确保中文字符在 JSON 文件中直接显示，而不是以 \uXXXX 形式显示
        const jsonString = JSON.stringify(data, null, 2)
            .replace(/\\u[\dA-Fa-f]{4}/g, match => {
                return JSON.parse(`"${match}"`);
            });

        await fs.writeFile(this.file, jsonString, 'utf8');
    }
}
