import {Roundtrip, ThreadStore} from '../message';
import {WorkflowRunner, WorkflowRunnerInit} from './workflow';
import {ToolCallWorkflowRunner, ToolCallWorkflowRunnerInit} from './tool';

export interface WorkflowDetectorInit {
    threadUuid: string;
    systemPrompt: string;
    store: ThreadStore;
    roundtrip: Roundtrip;
}

export class WorkflowDetector {
    private readonly threadUuid: string;

    private readonly systemPrompt: string;

    private readonly store: ThreadStore;

    private readonly roundtrip: Roundtrip;

    constructor(init: WorkflowDetectorInit) {
        this.threadUuid = init.threadUuid;
        this.systemPrompt = init.systemPrompt;
        this.roundtrip = init.roundtrip;
        this.store = init.store;
    }

    detectWorkflow(): WorkflowRunner | null {
        const messages = this.roundtrip.toMessages();
        const assistantTextMessage = this.roundtrip.getLatestTextMessageStrict();
        const toolCallMessage = assistantTextMessage.toToolCallMessage();

        if (!toolCallMessage) {
            return null;
        }

        const baseInit: Omit<WorkflowRunnerInit, 'workflow'> = {
            threadUuid: this.threadUuid,
            store: this.store,
            base: messages.filter(v => v !== assistantTextMessage),
            origin: toolCallMessage,
        };
        const workflow = this.roundtrip.startWorkflowResponse(toolCallMessage);
        const init: ToolCallWorkflowRunnerInit = {
            ...baseInit,
            workflow,
            systemPrompt: this.systemPrompt,
            origin: toolCallMessage,
        };
        return new ToolCallWorkflowRunner(init);
    }
}
