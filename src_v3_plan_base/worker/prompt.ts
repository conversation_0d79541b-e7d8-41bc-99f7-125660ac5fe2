import path from 'node:path';
import fs from 'node:fs/promises';
import {builtinTools} from '../tool';
import {EmbeddingContextMode, InboxPromptView, renderInboxSystemPrompt} from '../prompt';
import {stringifyError} from '../utils/error';
import {now} from '../utils/string';
import {CodePosition, EmbeddingSearchResultItem} from '../prompt/embedding';
import {GlobalConfig} from './interface';

interface EmbeddingSearchResponseItem {
    repo: string;
    path: string;
    type: string;
    content: string;
    contentStart: CodePosition;
    contentEnd: CodePosition;
    distance: number;
    language: string;
}

interface EmbeddingSearchResponse {
    data: EmbeddingSearchResponseItem[];
    status: string;
    message: string;
}

interface PromptResult {
    type: 'result';
    prompt: string;
}

export type SystemPromptYieldResult = PromptResult;

export interface SystemPromptOptions extends GlobalConfig {
    embeddingRepoId: string | null;
}

export class SystemPromptGenerator {
    private readonly embeddingMinDistance: number;

    private readonly embeddingExcludesLanguage: string[];

    private readonly embeddingConetxtMode: EmbeddingContextMode;

    private readonly embeddingRepoId: string | null;

    private userRequest = '';

    constructor(options: SystemPromptOptions) {
        this.embeddingMinDistance = options.embeddingMinDistance;
        this.embeddingConetxtMode = options.embeddingConetxtMode;
        this.embeddingExcludesLanguage = options.embeddingExcludesLanguage;
        this.embeddingRepoId = options.embeddingRepoId;
    }

    setUserRequest(userRequest: string) {
        this.userRequest = userRequest;
    }

    async renderSystemPrompt(): Promise<string> {
        const view: InboxPromptView = {
            tools: [],
            embedding: [],
            embeddingContextMode: this.embeddingConetxtMode,
            rootEntries: [],
        };

        const toolsView = this.createToolsView();
        Object.assign(view, toolsView);

        try {
            const rootEntriesView = await this.createRootEntriesView();
            Object.assign(view, rootEntriesView);
        }
        catch (ex) {
            throw new Error(`Read project root error: ${stringifyError(ex)}`);
        }

        try {
            const embeddingView = await this.createEmbeddingView();
            Object.assign(view, embeddingView);
        }
        catch (ex) {
            throw new Error(`Embedding search error: ${stringifyError(ex)}`);
        }

        const systemPrompt = await renderInboxSystemPrompt(view);
        return systemPrompt;
    }

    private createToolsView() {
        if (process.platform === 'darwin') {
            // TODO: Enable this tool when shipped with `ripgrep`
            return {tools: builtinTools.filter(v => v.name !== 'find_files_by_regex')};
        }

        return {tools: builtinTools};
    }

    private async createRootEntriesView(): Promise<Partial<InboxPromptView>> {
        const {globby} = await import('globby');
        const {WorkspaceFileStructure} = await import('../workflow/tool/tree');

        const files = await globby('**/*', {
            cwd: process.cwd(),
            absolute: false,
            expandDirectories: false,
            onlyFiles: false,
            gitignore: true,
            dot: false,
            markDirectories: true,
            ignore: ['.*', '**/.*']   // Exclude all hidden files and directories
        });

        const tree = new WorkspaceFileStructure();
        for (const file of files) {
            tree.add(file);
        }

        const {tree: rootEntries, totalCount, truncatedCount} = tree.toOverviewStructure();

        return {
            rootEntries,
            rootEntriesMetadata: {
                totalCount,
                truncatedCount,
            }
        };
    }

    private async createEmbeddingView(): Promise<Partial<InboxPromptView>> {
        try {
            const embedding = await this.searchEmbedding();
            return {embedding};
        }
        catch (ex) {
            console.error("error in embedding search: ");
            console.error(ex);
            return {embedding: []};
        }
    }

    private async searchEmbedding(): Promise<EmbeddingSearchResultItem[]> {
        return [];
        if (!this.embeddingRepoId) {
            return [];
        }

        const body = {
            query: [this.userRequest],
            repo: [this.embeddingRepoId],
            taskId: `knowledge+${now()}`,
        };
        const response = await fetch(
            'https://cs.baidu-int.com/nlcodesearch/v2/multiembeddingsearch',
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(body),
            }
        );

        if (!response.ok) {
            throw new Error(`status code ${response.status}`);
        }
        const {data, status, message} = await response.json() as EmbeddingSearchResponse;
        if (status !== 'ok') {
            throw new Error(message);
        }

        const cwd = process.cwd();
        const toResultItem = async (item: EmbeddingSearchResponseItem): Promise<EmbeddingSearchResultItem | null> => {
            try {
                const file = path.resolve(cwd, item.path.replace(/^\//, ''));
                const content = await fs.readFile(file, 'utf-8');
                return {
                    file: path.relative(cwd, file),
                    start: item.contentStart,
                    end: item.contentEnd,
                    content,
                };
            }
            catch (ex) {
                console.error(ex);
                return null;
            }
        };
        const source = data
            .filter(v => v.distance >= this.embeddingMinDistance)
            .filter(v => !this.embeddingExcludesLanguage.includes(v.language));
        const items = await Promise.all(source.map(toResultItem));
        return items.filter(v => !!v);
    }
}
