import {newUuid} from '../../utils/id';
import {ThreadStore, ToolCallMessage, ToolUseMessage} from '../../message';
import {createModelClient, ModelChatOptions} from '../../model';
import {WorkflowRunner, WorkflowRunnerInit, WorkflowRunResult} from '../workflow';
import {ToolImplement} from './implement';
import {fixToolCall, parseToolMessage, ToolCallFixOptions} from './fix';
import {isToolInputError, RequireFix} from './utils';

const RETRY_LIMIT = 3;

export interface ToolCallWorkflowRunnerInit extends WorkflowRunnerInit {
    systemPrompt: string;
    origin: ToolCallMessage;
}

export class ToolCallWorkflowRunner extends WorkflowRunner {
    private readonly implment: ToolImplement;

    private readonly message: ToolCallMessage;

    private readonly systemPrompt: string;

    private readonly store: ThreadStore;

    private retries = 0;

    constructor(init: ToolCallWorkflowRunnerInit) {
        super(init);
        this.store = init.store;
        this.message = init.origin;
        this.systemPrompt = init.systemPrompt;
        this.implment = new ToolImplement();
    }

    async execute(): Promise<WorkflowRunResult> {
        const toolInput = this.message.getToolCallInput();
        const result = await this.implment.callTool(toolInput);

        if (result.type === 'requireFix') {
            await this.fix(result);
            return this.retry();
        }

        if (isToolInputError(result)) {
            const fixOptions: ToolCallFixOptions = {
                input: this.message.getToolCallInput(),
                response: this.message.getTextContent(),
                error: result,
            };
            const newToolCall = await fixToolCall(fixOptions);
            this.origin.replaceToolCallInput(newToolCall);
            return this.retry();
        }

        // Add a header note to all tool outputs
        const output = `\n\n${result.output}`;
        const responseMessage = new ToolUseMessage(newUuid(), output);
        this.workflow.addReaction(responseMessage, true);
        await this.store.persist();
        return {finished: result.type === 'success' && result.finished};
    }

    private async retry() {
        this.retries++;

        if (this.retries >= RETRY_LIMIT) {
            this.origin.setError('Model generates an invalid tool call and oniichan is unable to fix it');
            return {finished: true};
        }

        return this.execute();
    }

    private async fix(input: RequireFix) {
        const messages = [
            ...(input.includesBase ? this.base : []),
            this.origin,
            new ToolUseMessage(newUuid(), input.prompt),
        ];
        const options: ModelChatOptions = {
            messages: messages.map(v => v.toChatInputPayload()).filter(v => !!v),
            systemPrompt: this.systemPrompt,
        };
        const model = createModelClient();
        const [response] = await model.chat(options);
        const toolCall = await parseToolMessage(response.content);
        this.origin.replaceToolCallInput(toolCall);
    }
}
