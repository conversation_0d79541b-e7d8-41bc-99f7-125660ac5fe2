import childProcess from 'node:child_process';
import {runCommandParameters, RunCommandParameter} from '../../tool';
import {stringifyError} from '../../utils/error';
import {resultMarkdown, ToolImplementBase, ToolRunResult} from './utils';

type ExecuteStatus = 'error' | 'success' | 'timeout';

export class RunCommandToolImplement extends ToolImplementBase<RunCommandParameter> {
    private readonly output: string[] = [];
    private currentProcess: childProcess.ChildProcess | null = null;

    constructor() {
        super(runCommandParameters);
    }

    protected parseArgs(args: Record<string, string>): RunCommandParameter {
        return {
            command: args.command,
        };
    }

    protected async execute(args: RunCommandParameter): Promise<ToolRunResult> {
        // 确保开始新命令前没有遗留的进程
        this.killProcess();
        try {
            const status = await Promise.race([this.executeCommand(args.command), this.reportTimeout()]);
            const output = this.output.join('');
            switch (status) {
                case 'success':
                    return {
                        type: 'success',
                        finished: false,
                        output: output
                            ? resultMarkdown(`Command executed, here is its output:`, output)
                            : `Command exited without any output`,
                    };
                case 'error':
                    return {
                        type: 'success',
                        finished: false,
                        output: output
                            ? resultMarkdown(`Command seems to be failed, here is its output:`, output)
                            : `Command failed without any output`,
                    };
                case 'timeout':
                    return {
                        type: 'success',
                        finished: false,
                        output: output
                            ? 'This command is still running, it can be a long running session such as a dev server, unfortunately we can\'t retrieve any command output at this time, please continue your work'
                            : resultMarkdown('This command is still running, here is its output so far:', output),
                    };
            }
        }
        catch (ex) {
            this.killProcess();
            return {
                type: 'executeError',
                output: `Unable to execute command \`${args.command}\`: ${stringifyError(ex)}`,
            };
        }
    }

    private killProcess() {
        if (this.currentProcess) {
            try {
                if (process.platform === 'win32') {
                    // Windows 需要特殊处理
                    childProcess.execSync(`taskkill /pid ${this.currentProcess.pid} /T /F`);
                } else {
                    // Unix-like 系统先发送 SIGTERM，给进程一个清理的机会
                    this.currentProcess.kill('SIGTERM');

                    // 给进程 2 秒时间来清理
                    setTimeout(() => {
                        if (this.currentProcess) {
                            // 如果进程还在，强制结束
                            this.currentProcess.kill('SIGKILL');
                        }
                    }, 2000);
                }
            } catch (error) {
                console.error('Error killing process:', error);
            }
        }
    }

    private async executeCommand(command: string): Promise<ExecuteStatus> {
        return new Promise((resolve, reject) => {
            try {
                this.currentProcess = childProcess.spawn(
                    command,
                    {
                        shell: true,
                        cwd: process.cwd(),
                        env: {...process.env},
                    }
                );

                this.currentProcess.stdout?.on('data', (v: string | Buffer) => this.output.push(v.toString()));
                this.currentProcess.stderr?.on('data', (v: string | Buffer) => this.output.push(v.toString()));

                this.currentProcess.on('error', (error) => {
                    console.error('Process error:', error);
                    this.killProcess();
                    reject(error);
                });

                this.currentProcess.on('exit', (code, signal) => {
                    this.currentProcess = null;
                    if (signal) {
                        // 如果进程被信号终止
                        resolve('error');
                    } else {
                        resolve(code === 0 ? 'success' : 'error');
                    }
                });
            } catch (error) {
                console.error('Spawn error:', error);
                this.killProcess();
                reject(error);
            }
        });
    }

    private async reportTimeout(): Promise<ExecuteStatus> {
        await new Promise(r => setTimeout(r, 30 * 1000));
        this.killProcess(); // 超时时杀掉进程
        return 'timeout';
    }
}
