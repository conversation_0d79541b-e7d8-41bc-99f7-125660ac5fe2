function calculateSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1;

    // Rule 2: Match after removing indentation
    const trimmed1 = str1.replace(/^\s+/gm, '');
    const trimmed2 = str2.replace(/^\s+/gm, '');
    if (trimmed1 === trimmed2) return 1;

    // Rule 3: Case-insensitive match
    if (str1.toLowerCase() === str2.toLowerCase()) return 1;

    // Rule 4: First and last line match
    const lines1 = str1.split('\n');
    const lines2 = str2.split('\n');
    if (lines1.length > 0 && lines2.length > 0 &&
        lines1[0] === lines2[0] &&
        lines1[lines1.length - 1] === lines2[lines2.length - 1]) {
        return 1;
    }

    // Rule 5: Match after removing all whitespace
    const noWhitespace1 = str1.replace(/\s+/g, '');
    const noWhitespace2 = str2.replace(/\s+/g, '');
    if (noWhitespace1 === noWhitespace2) return 1;

    if (str1.length === 0 || str2.length === 0) return 0;

    const matrix: number[][] = Array(str1.length + 1)
        .fill(null)
        .map(() => Array(str2.length + 1).fill(0));

    for (let i = 0; i <= str1.length; i++) matrix[i][0] = i;
    for (let j = 0; j <= str2.length; j++) matrix[0][j] = j;

    for (let i = 1; i <= str1.length; i++) {
        for (let j = 1; j <= str2.length; j++) {
            const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
            matrix[i][j] = Math.min(
                matrix[i - 1][j] + 1,
                matrix[i][j - 1] + 1,
                matrix[i - 1][j - 1] + cost
            );
        }
    }

    const maxLength = Math.max(str1.length, str2.length);
    return 1 - matrix[str1.length][str2.length] / maxLength;
}

export interface FuzzyMatchResult {
    content: string[];
    similarity: number;
    startIndex: number;
}

export function findMostSimilarContent(
    source: string[],
    target: string[],
    startSearchFrom: number = 0
): FuzzyMatchResult {
    const searchArea = source.slice(startSearchFrom);
    let bestMatch: FuzzyMatchResult = {
        content: [],
        similarity: 0,
        startIndex: -1
    };

    // Convert arrays to strings for comparison
    const targetStr = target.join('\n');

    for (let i = 0; i <= searchArea.length - target.length; i++) {
        const candidateLines = searchArea.slice(i, i + target.length);
        const candidateStr = candidateLines.join('\n');
        const similarity = calculateSimilarity(targetStr, candidateStr);

        if (similarity > bestMatch.similarity) {
            bestMatch = {
                content: candidateLines,
                similarity: similarity,
                startIndex: i + startSearchFrom
            };
        }
    }

    return bestMatch;
}
