import {ParsedPatch} from './parse';
import {FuzzyMatchResult, findMostSimilarContent} from './fuzzyMatch';

export interface PatchApplyResult {
    type: 'success' | 'partial';
    content: string;
    fuzzyMatches?: Array<{
        searchContent: string[];
        matchedContent: string[];
        similarity: number;
    }>;
    successCount?: number;
    totalCount?: number;
}

function locateLines(source: string[], target: string[]) {
    const startingLine = target.at(0);

    if (typeof startingLine !== 'string') {
        return -1;
    }

    const followingLinesCount = target.length - 1;
    const followingLinesInTarget = target.slice(1);
    for (let i = 0; i < source.length - followingLinesCount; i++) {
        const line = source[i];
        if (line === startingLine) {
            const followingLinesInSource = source.slice(i + 1, i + 1 + followingLinesCount);
            if (followingLinesInSource.every((v, i) => v === followingLinesInTarget[i])) {
                return i;
            }
        }
    }

    return -1;
}

function applySinglePatch(
    oldLines: string[],
    patch: ParsedPatch,
    startSearchFrom: number = 0
): { lines: string[], endIndex: number } {
    // 只在startSearchFrom之后的部分中查找
    const searchArea = oldLines.slice(startSearchFrom);
    const relativeIndex = locateLines(searchArea, patch.search);

    if (relativeIndex < 0) {
        // 尝试模糊匹配，找到最相似的内容
        const fuzzyMatch = findMostSimilarContent(searchArea, patch.search);
        const error = new Error('Search content not found');
        // 附加模糊匹配信息到错误对象
        (error as any).fuzzyMatches = [{
            searchContent: patch.search,
            matchedContent: fuzzyMatch.content,
            similarity: fuzzyMatch.similarity
        }];
        throw error;
    }

    // 将相对索引转换为绝对索引
    const absoluteIndex = startSearchFrom + relativeIndex;

    return {
        lines: [
            ...oldLines.slice(0, absoluteIndex),
            ...patch.replace,
            ...oldLines.slice(absoluteIndex + patch.search.length),
        ],
        endIndex: absoluteIndex + patch.replace.length
    };
}

export function applyPatch(oldContent: string, patches: ParsedPatch[]): PatchApplyResult {
    const lines = oldContent.split('\n');
    let currentLines = lines;
    let lastEndIndex = 0;
    let successCount = 0;
    const totalCount = patches.length;
    const failedPatches: Array<{
        searchContent: string[];
        matchedContent: string[];
        similarity: number;
    }> = [];

    for (const patch of patches) {
        try {
            const result = applySinglePatch(currentLines, patch, lastEndIndex);
            currentLines = result.lines;
            lastEndIndex = result.endIndex;
            successCount++;
        } catch (error) {
            if (error instanceof Error && (error as any).fuzzyMatches) {
                failedPatches.push(...(error as any).fuzzyMatches);
                // 在第一个不匹配的补丁处停止
                break;
            } else {
                throw error;
            }
        }
    }

    if (successCount === totalCount) {
        return {
            type: 'success',
            content: currentLines.join('\n')
        };
    } else {
        return {
            type: 'partial',
            content: currentLines.join('\n'),
            fuzzyMatches: failedPatches,
            successCount,
            totalCount
        };
    }
}
