export interface ParsedPatch {
    search: string[];
    replace: string[];
}

interface ParseState {
    context: 'none' | 'search' | 'replace';
    current: ParsedPatch | null;
}

export class PatchParseError extends Error {
    constructor(message: string, public readonly errorType?:
        'MISSING_SEARCH_DELIMITER' |
        'MISSING_REPLACE_DELIMITER' |
        'MISSING_END_DELIMITER' |
        'UNEXPECTED_CONTENT' |
        'INVALID_DELIMITER_ORDER' |
        'NO_PATCH_BLOCKS'
    ) {
        super(message);
    }
}

export function parsePatchString(input: string): ParsedPatch[] {
    // A patch block looks like this:
    //
    // ```
    // <<<SEARCH<<<
    // content to search
    // ====REPLACED_BY====
    // content to replace
    // >>>END>>>
    // ```
    //
    // For more stable parsing, we allow `<<<<`, `====` and `>>>>` to have different repetive length,
    // but it requires at least 4 in order not to be confused with the `<<`, `>>` or `===` operators
    const results: ParsedPatch[] = [];
    const state: ParseState = {context: 'none', current: null};

    for (const line of input.split('\n')) {
        if (/^\s*[<=]+\s*SEARCH\s*[<=]+\s*$/.test(line)) {
            if (state.context === 'replace') {
                throw new PatchParseError(
                    'Invalid delimiter order: SEARCH delimiter after REPLACED_BY',
                    'INVALID_DELIMITER_ORDER'
                );
            }
            if (state.context === 'search') {
                throw new PatchParseError(
                    'Duplicate SEARCH delimiter without REPLACED_BY and END',
                    'INVALID_DELIMITER_ORDER'
                );
            }

            if (state.current) {
                results.push(state.current);
            }

            state.context = 'search';
            state.current = {search: [], replace: []};
        }
        else if (/^\s*[<=>]+\s*REPLACED_BY\s*[<=>]+\s*$/.test(line)) {
            if (state.context !== 'search') {
                throw new PatchParseError(
                    'REPLACED_BY delimiter must follow SEARCH delimiter',
                    'INVALID_DELIMITER_ORDER'
                );
            }
            state.context = 'replace';
        }
        else if (/^\s*[=>]+\s*END\s*[=>]+\s*$/.test(line)) {
            if (state.context !== 'replace') {
                throw new PatchParseError(
                    'END delimiter must follow REPLACED_BY delimiter',
                    'INVALID_DELIMITER_ORDER'
                );
            }
            state.context = 'none';
        }
        else {
            if ((state.context === 'none' || state.context === null) && line.trim().length > 0) {
                throw new PatchParseError(
                    'Content outside of patch block',
                    'UNEXPECTED_CONTENT'
                );
            }
            if (state.context === 'search' && state.current) {
                state.current.search.push(line);
            }
            else if (state.context === 'replace' && state.current) {
                state.current.replace.push(line);
            }
        }
    }

    if (state.current) {
        results.push(state.current);
    }

    if (results.length === 0) {
        throw new PatchParseError(
            'No patch blocks found in input string',
            'NO_PATCH_BLOCKS'
        );
    }

    if (state.context !== 'none') {
        throw new PatchParseError(
            `Incomplete patch block - missing ${state.context === 'search' ? 'REPLACED_BY and END' : 'END'} delimiter`,
            state.context === 'search' ? 'MISSING_REPLACE_DELIMITER' : 'MISSING_END_DELIMITER'
        );
    }

    return results;
}
